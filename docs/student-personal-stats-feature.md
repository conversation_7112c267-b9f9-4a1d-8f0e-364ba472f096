# 学生个人勤工助学统计功能

## 功能概述

为学工管理系统新增了学生个人勤工助学统计功能，允许学生查看自己的勤工助学相关数据统计，同时为管理员提供查看指定学生统计数据的功能。

## 功能特性

### 1. 学生个人统计数据

- **我申请的岗位数**：学生已提交申请的岗位总数
- **待面试岗位数**：面试结果为"待面试"状态的记录数
- **已录用岗位数**：已被录用的岗位数
- **待签到岗位数**：已录用但还未开始签到的岗位数
- **工作中岗位数**：正在工作中的岗位数
- **已完成岗位数**：已完成工作的岗位数
- **本月考勤次数**：当前月份的考勤签到次数
- **累计工作时长**：累计的工作时长（小时）

### 2. 用户权限控制

- **学生权限**：`workstudy:student:personal-stats` - 查看自己的统计数据
- **管理员权限**：`workstudy:admin:student-stats` - 查看指定学生的统计数据

## API接口

### 1. 获取当前登录学生的个人统计数据

```http
GET /api/workstudy/dashboard/student/personal-stats
```

**权限要求**：`workstudy:student:personal-stats`

**响应示例**：
```json
{
  "studentId": "student-001",
  "studentName": "张三",
  "studentNumber": "2021001001",
  "myAppliedJobs": 5,
  "myPendingInterviews": 2,
  "myAcceptedJobs": 3,
  "myPendingCheckIn": 1,
  "myWorkingJobs": 2,
  "myCompletedJobs": 1,
  "monthlyAttendance": 15,
  "totalWorkHours": 120.5
}
```

### 2. 获取指定学生的个人统计数据（管理员使用）

```http
GET /api/workstudy/dashboard/student/{studentId}/stats
```

**权限要求**：`workstudy:admin:student-stats`

**路径参数**：
- `studentId`：学生ID

**响应格式**：同上

## 技术实现

### 1. 数据传输对象

**QgzxStudentPersonalStatsDTO**
- 包含学生基本信息和8个统计指标
- 提供默认构造函数和带参构造函数
- 自动处理null值，默认为0

### 2. 数据访问层

**QgzxDashboardMapper**
- 新增8个学生个人统计查询方法
- 使用@Select注解定义SQL查询
- 支持按学生ID进行精确统计

### 3. 业务逻辑层

**QgzxDashboardService**
- `getStudentPersonalStats(String studentId)` - 获取指定学生统计
- `getCurrentStudentStats()` - 获取当前登录学生统计

**QgzxDashboardServiceImpl**
- 集成SecurityUtil获取当前登录用户信息
- 完善的异常处理机制
- 详细的日志记录

### 4. 控制器层

**QgzxDashboardController**
- 新增两个REST接口
- 集成权限控制和操作日志
- 支持路径参数和用户身份识别

## 数据库查询逻辑

### 1. 核心统计查询

```sql
-- 学生申请的岗位数
SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY WHERE STUDENT_ID = ?

-- 待面试岗位数
SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir 
INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID 
WHERE sa.STUDENT_ID = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi'

-- 已录用岗位数
SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY 
WHERE STUDENT_ID = ? AND SPZT = 'TongGuo' AND YGZT IN ('DSG', 'YG')

-- 本月考勤次数
SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar 
INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID 
WHERE sa.STUDENT_ID = ? 
AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') 
AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)

-- 累计工作时长
SELECT NVL(SUM(ar.WORK_HOURS), 0) FROM SYT_QGZX_ATTENDANCE_RECORD ar 
INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID 
WHERE sa.STUDENT_ID = ?
```

### 2. 状态枚举对应

- **用工状态**：DSG(待上岗)、YG(用工)、YGJS(用工结束)
- **审批状态**：TongGuo(通过)
- **面试结果**：DaiMianShi(待面试)

## 安全特性

### 1. 用户身份验证

- 使用SecurityUtil获取当前登录用户信息
- 自动识别学生身份和权限
- 防止越权访问其他学生数据

### 2. 数据权限控制

- 学生只能查看自己的统计数据
- 管理员可以查看指定学生的统计数据
- 基于Spring Security的权限控制

### 3. 异常处理

- 完善的异常捕获和处理
- 返回默认值避免前端报错
- 详细的错误日志记录

## 测试验证

### 1. 单元测试

**QgzxDashboardServiceTest**
- 测试学生个人统计数据获取
- Mock数据验证业务逻辑
- 性能测试和异常处理测试

### 2. 测试结果

```
Tests run: 1, Failures: 0, Errors: 0, Skipped: 0
学生个人统计数据: 申请岗位数=5, 待面试数=2, 已录用数=3, 待签到数=1, 工作中数=2, 已完成数=1, 本月考勤=15, 累计工时=120.5
```

## 部署说明

### 1. 数据库要求

- 确保相关表存在：SYT_QGZX_STUDENT_APPLY、SYT_QGZX_INTERVIEW_RECORD、SYT_QGZX_ATTENDANCE_RECORD
- 建议在STUDENT_ID字段上建立索引以提高查询性能

### 2. 权限配置

需要在系统中配置以下权限：
- `workstudy:student:personal-stats` - 学生个人统计权限
- `workstudy:admin:student-stats` - 管理员查看学生统计权限

### 3. 前端集成

可以通过以下方式调用API：
```javascript
// 获取当前学生统计
const studentStats = await request.get('/api/workstudy/dashboard/student/personal-stats');

// 管理员获取指定学生统计
const studentStats = await request.get(`/api/workstudy/dashboard/student/${studentId}/stats`);
```

## 扩展功能建议

### 1. 时间维度统计

- 按学期统计
- 按年度统计
- 历史趋势分析

### 2. 可视化展示

- 统计图表展示
- 进度条显示
- 对比分析

### 3. 导出功能

- Excel导出个人统计报告
- PDF格式统计报告
- 邮件发送统计报告

## 版本历史

- **v1.0.0** (2025-08-08)：初始版本
  - 学生个人统计功能
  - 8个核心统计指标
  - 权限控制和安全验证
  - 完整的单元测试
