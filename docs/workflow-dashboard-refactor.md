# 工作流统计功能重构文档

## 重构概述

根据需求，将通用工作流统计功能从qgzx模块移动到workflow模块，实现模块职责的清晰分离。

## 重构目标

- **模块分离**：将通用工作流统计功能从勤工助学模块分离到工作流模块
- **职责明确**：qgzx模块专注于勤工助学业务，workflow模块负责通用工作流统计
- **代码复用**：workflow模块的统计功能可被其他模块复用
- **架构清晰**：遵循单一职责原则，提高代码可维护性

## 重构内容

### 1. 新增workflow模块文件

#### **数据传输对象**
- `WorkflowDashboardStatsDTO.java` - 通用工作流统计数据DTO

#### **数据访问层**
- `WorkflowDashboardMapper.java` - 工作流统计Mapper接口
- `WorkflowDashboardMapper.xml` - 工作流统计SQL映射文件

#### **业务逻辑层**
- `WorkflowDashboardService.java` - 工作流统计服务接口
- `WorkflowDashboardServiceImpl.java` - 工作流统计服务实现

#### **控制器层**
- `WorkflowDashboardController.java` - 更新为使用workflow模块自己的服务

#### **测试文件**
- `WorkflowDashboardServiceTest.java` - 工作流统计功能测试

### 2. 从qgzx模块移除的内容

#### **删除的文件**
- `WorkflowDashboardStatsDTO.java` - 已移动到workflow模块

#### **修改的文件**
- `QgzxDashboardService.java` - 移除工作流统计方法
- `QgzxDashboardServiceImpl.java` - 移除工作流统计实现
- `QgzxDashboardMapper.java` - 移除工作流相关查询方法
- `QgzxDashboardMapper.xml` - 移除工作流相关SQL查询

## 功能特性

### 1. 通用工作流统计

**基础统计功能**：
- 待审批数统计
- 通过数统计
- 不通过数统计
- 退回数统计

**高级统计功能**：
- 按时间范围统计
- 按模块代码统计
- 按审批人统计
- 按申请人统计

### 2. API接口设计

#### **通用统计接口**
```http
GET /api/workflow/dashboard/stats
权限：workflow:dashboard:stats
```

#### **时间范围统计接口**
```http
GET /api/workflow/dashboard/stats/date-range?startDate=2025-01-01 00:00:00&endDate=2025-12-31 23:59:59
权限：workflow:dashboard:stats
```

#### **模块统计接口**
```http
GET /api/workflow/dashboard/stats/modules?moduleCodes=qgzxGwsb,qgzxBcsb
权限：workflow:dashboard:stats
```

#### **审批人统计接口**
```http
GET /api/workflow/dashboard/approver/current-stats
权限：workflow:approver:stats

GET /api/workflow/dashboard/approver/{approverId}/stats
权限：workflow:admin:approver-stats
```

#### **申请人统计接口**
```http
GET /api/workflow/dashboard/applicant/current-stats
权限：workflow:applicant:stats

GET /api/workflow/dashboard/applicant/{applicantId}/stats
权限：workflow:admin:applicant-stats
```

## 技术实现

### 1. 数据库查询优化

**一次性查询所有统计**：
```sql
SELECT 
    SUM(CASE WHEN RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
    SUM(CASE WHEN RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
    SUM(CASE WHEN RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD
```

**按模块统计**：
```sql
SELECT ... FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
WHERE w.MODULE_CODE IN (#{moduleCodes})
```

### 2. 权限控制

**权限分级设计**：
- `workflow:dashboard:stats` - 通用工作流统计权限
- `workflow:approver:stats` - 审批人个人统计权限
- `workflow:applicant:stats` - 申请人个人统计权限
- `workflow:admin:approver-stats` - 管理员查看审批人统计权限
- `workflow:admin:applicant-stats` - 管理员查看申请人统计权限

### 3. 用户身份识别

**当前用户获取**：
```java
SysAccount account = SecurityUtil.getAccount();
String userId = account.getId();
```

**角色区分**：
- 审批人：查看自己负责审批的工作流统计
- 申请人：查看自己提交申请的工作流统计
- 管理员：可查看指定用户的统计数据

## 代码质量

### 1. 遵循项目规范

- **命名规范**：使用项目统一的命名约定
- **注释完整**：所有关键方法都有详细的中文注释
- **异常处理**：完善的错误处理机制
- **日志记录**：详细的操作日志和调试信息

### 2. 架构设计

- **分层架构**：Controller → Service → Mapper 清晰分层
- **依赖注入**：使用Spring依赖注入管理组件
- **接口设计**：面向接口编程，便于扩展和测试

### 3. 测试覆盖

**单元测试**：
- 基础统计功能测试
- 高级统计功能测试
- 异常处理测试
- Mock测试验证业务逻辑

## 部署说明

### 1. 数据库要求

确保以下表存在并有适当索引：
- `SYT_WORKFLOW_APPROVAL_NODE_RECORD` - 工作流审批记录表
- `SYT_WORKFLOW_APPROVAL_NODE` - 工作流审批节点表
- `SYT_WORKFLOW` - 工作流表

### 2. 权限配置

需要在系统中配置以下权限：
- `workflow:dashboard:stats`
- `workflow:approver:stats`
- `workflow:applicant:stats`
- `workflow:admin:approver-stats`
- `workflow:admin:applicant-stats`

### 3. 前端调用

```javascript
// 获取通用工作流统计
const workflowStats = await request.get('/api/workflow/dashboard/stats');

// 获取当前审批人统计
const approverStats = await request.get('/api/workflow/dashboard/approver/current-stats');

// 获取当前申请人统计
const applicantStats = await request.get('/api/workflow/dashboard/applicant/current-stats');
```

## 重构优势

### 1. 模块职责清晰

- **qgzx模块**：专注于勤工助学业务逻辑
- **workflow模块**：负责通用工作流功能

### 2. 代码复用性

- workflow模块的统计功能可被其他业务模块复用
- 避免重复开发类似的统计功能

### 3. 可维护性提升

- 单一职责原则，降低模块间耦合
- 便于独立测试和维护

### 4. 扩展性增强

- 易于添加新的统计维度
- 支持更多业务场景的统计需求

## 版本历史

- **v1.0.0** (2025-08-08)：工作流统计功能重构
  - 从qgzx模块迁移到workflow模块
  - 新增多维度统计功能
  - 完善权限控制和用户身份识别
  - 完整的单元测试覆盖
