# 工作流统计功能增强 - 支持多条件查询

## 功能概述

为工作流管理模块的统计功能增加了多条件查询支持，允许按年份(YEAR)、项目ID(PROJECT_ID)或模块代码(MODULE_CODE)等条件统计不同ReviewResult类型的数据。

## 新增功能特性

### 1. 查询条件支持

**工作流条件**：
- **年份 (YEAR)**：按指定年份筛选工作流统计数据
- **项目ID (PROJECT_ID)**：按特定项目进行统计
- **模块代码 (MODULE_CODE)**：按模块代码筛选，如 "qgzxGwsb"、"qgzxBcsb"等

**时间范围查询**：
- **开始时间 (START_TIME)**：用于审批记录的时间范围筛选
- **结束时间 (END_TIME)**：用于审批记录的时间范围筛选

### 2. ReviewResult统计类型

统计不同审批结果类型的数据：
- ✅ **待审批数 (pendingApprovals)**：RESULT = 'DaiShenPi'
- ✅ **通过数 (approved)**：RESULT = 'TongGuo'
- ✅ **不通过数 (rejected)**：RESULT = 'BuTongGuo'
- ✅ **退回数 (returned)**：RESULT = 'TuiHui'

## 技术实现

### 1. 数据传输对象

**WorkflowStatsQueryDTO**：
```java
@Data
public class WorkflowStatsQueryDTO {
    private String year;        // 年份（可选）
    private String projectId;   // 项目ID（可选）
    private String moduleCode;  // 模块代码（可选）
    private String startTime;   // 开始时间（可选）
    private String endTime;     // 结束时间（可选）
    
    // 工具方法
    public boolean hasConditions();         // 检查是否有查询条件
    public boolean hasTimeRange();          // 检查是否有时间范围条件
    public boolean hasWorkflowConditions(); // 检查是否有工作流相关条件
}
```

### 2. 数据访问层增强

**WorkflowDashboardMapper** 新增5个按条件查询的方法：
- `countWorkflowStatsByYear()` - 按年份统计工作流审批数据
- `countWorkflowStatsByProjectId()` - 按项目ID统计工作流审批数据
- `countWorkflowStatsByModuleCode()` - 按模块代码统计工作流审批数据
- `countWorkflowStatsWithConditions()` - 按多条件统计工作流审批数据

### 3. SQL查询实现

**按年份统计示例**：
```xml
<select id="countWorkflowStatsByYear" resultType="java.util.Map">
    SELECT 
        SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
        SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
    FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
    INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
    INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
    WHERE w.YEAR = #{year}
</select>
```

**多条件动态查询示例**：
```xml
<select id="countWorkflowStatsWithConditions" resultType="java.util.Map">
    SELECT 
        SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
        SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
    FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
    INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
    INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
    <where>
        <if test="year != null and year != ''">
            AND w.YEAR = #{year}
        </if>
        <if test="projectId != null and projectId != ''">
            AND w.PROJECT_ID = #{projectId}
        </if>
        <if test="moduleCode != null and moduleCode != ''">
            AND w.MODULE_CODE = #{moduleCode}
        </if>
        <if test="startTime != null and startTime != ''">
            AND wr.CREATE_TIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime != ''">
            AND wr.CREATE_TIME &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
    </where>
</select>
```

### 4. 业务逻辑层增强

**WorkflowDashboardService** 新增方法：
- `getWorkflowStatsByYear(String year)` - 按年份获取工作流统计
- `getWorkflowStatsByProjectId(String projectId)` - 按项目ID获取工作流统计
- `getWorkflowStatsByModuleCode(String moduleCode)` - 按模块代码获取工作流统计
- `getWorkflowStatsWithConditions(WorkflowStatsQueryDTO queryDTO)` - 按多条件获取工作流统计

**WorkflowDashboardServiceImpl** 实现特性：
- ✅ 完善的参数验证
- ✅ 详细的日志记录
- ✅ 异常处理机制
- ✅ 统一的返回格式

### 5. 控制器层API设计

**新增5个API接口**：

#### **按年份统计接口**
```http
GET /api/workflow/dashboard/stats/year/{year}
权限：workflow:dashboard:stats

示例：GET /api/workflow/dashboard/stats/year/2024
```

#### **按项目ID统计接口**
```http
GET /api/workflow/dashboard/stats/project/{projectId}
权限：workflow:dashboard:stats

示例：GET /api/workflow/dashboard/stats/project/proj-001
```

#### **按模块代码统计接口**
```http
GET /api/workflow/dashboard/stats/module/{moduleCode}
权限：workflow:dashboard:stats

示例：GET /api/workflow/dashboard/stats/module/qgzxGwsb
```

#### **POST接口 - 多条件查询**
```http
POST /api/workflow/dashboard/stats/conditions
权限：workflow:dashboard:stats

请求体示例：
{
  "year": "2024",
  "projectId": "proj-001",
  "moduleCode": "qgzxGwsb",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

#### **GET接口 - 参数查询**
```http
GET /api/workflow/dashboard/stats/query?year=2024&projectId=proj-001&moduleCode=qgzxGwsb&startTime=2024-01-01 00:00:00&endTime=2024-12-31 23:59:59
权限：workflow:dashboard:stats
```

## 数据库表关联

### 1. 核心表结构

**主要关联表**：
- `SYT_WORKFLOW` - 工作流表（包含YEAR、PROJECT_ID、MODULE_CODE等字段）
- `SYT_WORKFLOW_APPROVAL_NODE` - 工作流审批节点表
- `SYT_WORKFLOW_APPROVAL_NODE_RECORD` - 工作流审批记录表（包含RESULT字段）

### 2. 字段命名规范

严格遵循项目现有的字段命名规范：
- **YEAR** - 年份
- **PROJECT_ID** - 项目ID
- **MODULE_CODE** - 模块代码
- **RESULT** - 审批结果
- **CREATE_TIME** - 创建时间

### 3. 审批结果枚举值

**审批结果 (RESULT)**：
- `DaiShenPi` - 待审批
- `TongGuo` - 通过
- `BuTongGuo` - 不通过
- `TuiHui` - 退回

## 使用示例

### 1. 前端调用示例

```javascript
// 按年份查询工作流统计
const yearStats = await request.get('/api/workflow/dashboard/stats/year/2024');

// 按项目ID查询工作流统计
const projectStats = await request.get('/api/workflow/dashboard/stats/project/proj-001');

// 按模块代码查询工作流统计
const moduleStats = await request.get('/api/workflow/dashboard/stats/module/qgzxGwsb');

// 多条件查询工作流统计
const queryConditions = {
  year: "2024",
  projectId: "proj-001",
  moduleCode: "qgzxGwsb",
  startTime: "2024-01-01 00:00:00",
  endTime: "2024-12-31 23:59:59"
};
const conditionStats = await request.post('/api/workflow/dashboard/stats/conditions', queryConditions);

// GET方式参数查询
const getStats = await request.get('/api/workflow/dashboard/stats/query?year=2024&moduleCode=qgzxGwsb');
```

### 2. 响应数据格式

```json
{
  "pendingApprovals": 25,
  "approved": 120,
  "rejected": 8,
  "returned": 5
}
```

## 性能优化

### 1. 数据库优化

**建议索引**：
```sql
-- 工作流表索引
CREATE INDEX IDX_WORKFLOW_YEAR ON SYT_WORKFLOW(YEAR);
CREATE INDEX IDX_WORKFLOW_PROJECT_ID ON SYT_WORKFLOW(PROJECT_ID);
CREATE INDEX IDX_WORKFLOW_MODULE_CODE ON SYT_WORKFLOW(MODULE_CODE);

-- 审批记录表索引
CREATE INDEX IDX_APPROVAL_RECORD_RESULT ON SYT_WORKFLOW_APPROVAL_NODE_RECORD(RESULT);
CREATE INDEX IDX_APPROVAL_RECORD_CREATE_TIME ON SYT_WORKFLOW_APPROVAL_NODE_RECORD(CREATE_TIME);
CREATE INDEX IDX_APPROVAL_RECORD_NODE_ID ON SYT_WORKFLOW_APPROVAL_NODE_RECORD(NODE_ID);

-- 审批节点表索引
CREATE INDEX IDX_APPROVAL_NODE_WORKFLOW_ID ON SYT_WORKFLOW_APPROVAL_NODE(WORKFLOW_ID);
```

### 2. 查询优化

- ✅ 使用动态SQL避免不必要的条件判断
- ✅ 合理使用INNER JOIN提高查询效率
- ✅ 使用CASE WHEN进行一次性统计，减少多次查询
- ✅ 时间范围查询使用Oracle日期函数优化

## 扩展功能建议

### 1. 统计维度扩展

- 按申请人统计
- 按审批人统计
- 按部门统计
- 按时间段统计（周、月、季度）

### 2. 可视化展示

- 统计图表展示
- 趋势分析图
- 对比分析图

### 3. 导出功能

- Excel导出统计报告
- PDF格式统计报告
- 定时统计报告

## 版本历史

- **v1.2.0** (2025-08-08)：工作流统计多条件查询功能增强
  - 新增年份、项目ID、模块代码查询条件
  - 新增时间范围查询支持
  - 新增5个API接口
  - 完善的参数验证和异常处理
  - 遵循项目代码规范和字段命名约定
