# 勤工助学统计功能增强 - 支持多条件查询

## 功能概述

为勤工助学统计功能增加了多条件查询支持，允许按学年学期(XNXQ)、岗位ID(JOB_ID)和岗位类别(SYT_QGZX_JOB_TYPE.NAME)等条件进行精确统计查询。

## 新增功能特性

### 1. 查询条件支持

**基础查询条件**：
- **学年学期 (XNXQ)**：按指定学年学期筛选统计数据，如 "2024-2025-1"
- **岗位ID (JOB_ID)**：按特定岗位进行统计
- **岗位类别名称 (JOB_TYPE_NAME)**：按岗位类别筛选，如 "图书管理员"、"实验室助理"等

**时间范围查询**：
- **开始日期 (START_DATE)**：用于考勤和工作时长的时间范围筛选
- **结束日期 (END_DATE)**：用于考勤和工作时长的时间范围筛选

### 2. 统计指标覆盖

所有8个学生个人统计指标均支持条件查询：
- ✅ 我申请的岗位数
- ✅ 待面试岗位数
- ✅ 已录用岗位数
- ✅ 待签到岗位数
- ✅ 工作中岗位数
- ✅ 已完成岗位数
- ✅ 考勤次数（支持时间范围）
- ✅ 工作时长（支持时间范围）

## 技术实现

### 1. 数据传输对象

**QgzxStudentStatsQueryDTO**：
```java
@Data
public class QgzxStudentStatsQueryDTO {
    private String xgh;           // 学号（必填）
    private String xnxq;          // 学年学期（可选）
    private String jobId;         // 岗位ID（可选）
    private String jobTypeName;   // 岗位类别名称（可选）
    private String startDate;     // 开始日期（可选）
    private String endDate;       // 结束日期（可选）
    
    // 工具方法
    public boolean hasConditions();  // 检查是否有查询条件
    public boolean hasDateRange();   // 检查是否有时间范围条件
}
```

### 2. 数据访问层增强

**QgzxDashboardMapper** 新增8个带条件查询的方法：
- `countStudentAppliedJobsWithConditions()` - 按条件统计申请岗位数
- `countStudentPendingInterviewsWithConditions()` - 按条件统计待面试岗位数
- `countStudentAcceptedJobsWithConditions()` - 按条件统计已录用岗位数
- `countStudentPendingCheckInWithConditions()` - 按条件统计待签到岗位数
- `countStudentWorkingJobsWithConditions()` - 按条件统计工作中岗位数
- `countStudentCompletedJobsWithConditions()` - 按条件统计已完成岗位数
- `countStudentAttendanceWithConditions()` - 按条件统计考勤次数
- `countStudentWorkHoursWithConditions()` - 按条件统计工作时长

### 3. SQL查询优化

**动态条件查询示例**：
```xml
<select id="countStudentAppliedJobsWithConditions" resultType="java.lang.Long">
    SELECT COUNT(*)
    FROM SYT_QGZX_STUDENT_APPLY sa
    INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
    <if test="jobTypeName != null and jobTypeName != ''">
        INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
    </if>
    <where>
        sa.XGH = #{xgh}
        <if test="xnxq != null and xnxq != ''">
            AND sa.XNXQ = #{xnxq}
        </if>
        <if test="jobId != null and jobId != ''">
            AND sa.JOB_ID = #{jobId}
        </if>
        <if test="jobTypeName != null and jobTypeName != ''">
            AND jt.NAME = #{jobTypeName}
        </if>
    </where>
</select>
```

**时间范围查询示例**：
```xml
<select id="countStudentAttendanceWithConditions" resultType="java.lang.Long">
    SELECT COUNT(*)
    FROM SYT_QGZX_ATTENDANCE_RECORD ar
    INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
    INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
    <if test="jobTypeName != null and jobTypeName != ''">
        INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
    </if>
    <where>
        sa.XGH = #{xgh}
        <!-- 动态条件 -->
        <if test="startDate != null and startDate != ''">
            AND ar.ATTENDANCE_DATE >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
        </if>
        <if test="endDate != null and endDate != ''">
            AND ar.ATTENDANCE_DATE &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')
        </if>
        <!-- 默认本月查询 -->
        <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">
            AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
            AND ar.ATTENDANCE_DATE &lt; ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
        </if>
    </where>
</select>
```

### 4. 业务逻辑层增强

**QgzxDashboardService** 新增方法：
- `getStudentPersonalStatsWithConditions(QgzxStudentStatsQueryDTO queryDTO)` - 按条件获取学生统计
- `getCurrentStudentStatsWithConditions(QgzxStudentStatsQueryDTO queryDTO)` - 按条件获取当前学生统计

**QgzxDashboardServiceImpl** 实现特性：
- ✅ 完善的参数验证
- ✅ 详细的日志记录
- ✅ 异常处理机制
- ✅ 自动设置当前用户学号

### 5. 控制器层API设计

**新增3个API接口**：

#### **POST接口 - 当前学生条件查询**
```http
POST /api/workstudy/dashboard/student/personal-stats/conditions
权限：workstudy:student:personal-stats

请求体示例：
{
  "xnxq": "2024-2025-1",
  "jobTypeName": "图书管理员",
  "startDate": "2024-09-01",
  "endDate": "2024-12-31"
}
```

#### **POST接口 - 管理员指定学生条件查询**
```http
POST /api/workstudy/dashboard/student/stats/conditions
权限：workstudy:admin:student-stats

请求体示例：
{
  "xgh": "2021001001",
  "xnxq": "2024-2025-1",
  "jobId": "job-001",
  "jobTypeName": "实验室助理"
}
```

#### **GET接口 - 当前学生参数查询**
```http
GET /api/workstudy/dashboard/student/personal-stats/query?xnxq=2024-2025-1&jobTypeName=图书管理员&startDate=2024-09-01&endDate=2024-12-31
权限：workstudy:student:personal-stats
```

## 数据库表关联

### 1. 核心表结构

**主要关联表**：
- `SYT_QGZX_STUDENT_APPLY` - 学生申请表（包含XGH、XNXQ、JOB_ID等字段）
- `SYT_QGZX_JOB_APPLICATION` - 岗位申请表（包含JOB_TYPE_ID等字段）
- `SYT_QGZX_JOB_TYPE` - 岗位类别表（包含NAME字段）
- `SYT_QGZX_INTERVIEW_RECORD` - 面试记录表
- `SYT_QGZX_ATTENDANCE_RECORD` - 考勤记录表

### 2. 字段命名规范

严格遵循项目现有的字段命名规范：
- **XGH** - 学号
- **XNXQ** - 学年学期
- **JOB_ID** - 岗位ID
- **JOB_TYPE_ID** - 岗位类别ID
- **NAME** - 岗位类别名称
- **SPZT** - 审批状态
- **YGZT** - 用工状态
- **SFQD** - 是否签到

### 3. 状态枚举值

**审批状态 (SPZT)**：
- `TongGuo` - 通过

**用工状态 (YGZT)**：
- `DSG` - 待上岗
- `YG` - 用工
- `YGJS` - 用工结束

**面试结果**：
- `DaiMianShi` - 待面试

## 使用示例

### 1. 前端调用示例

```javascript
// 按学年学期查询当前学生统计
const queryByTerm = {
  xnxq: "2024-2025-1"
};
const termStats = await request.post('/api/workstudy/dashboard/student/personal-stats/conditions', queryByTerm);

// 按岗位类别查询当前学生统计
const queryByJobType = {
  jobTypeName: "图书管理员"
};
const jobTypeStats = await request.post('/api/workstudy/dashboard/student/personal-stats/conditions', queryByJobType);

// 按时间范围查询考勤统计
const queryByDateRange = {
  startDate: "2024-09-01",
  endDate: "2024-12-31"
};
const attendanceStats = await request.post('/api/workstudy/dashboard/student/personal-stats/conditions', queryByDateRange);

// GET方式查询（适合简单条件）
const getStats = await request.get('/api/workstudy/dashboard/student/personal-stats/query?xnxq=2024-2025-1&jobTypeName=图书管理员');
```

### 2. 管理员查询示例

```javascript
// 管理员查询指定学生的条件统计
const adminQuery = {
  xgh: "2021001001",
  xnxq: "2024-2025-1",
  jobTypeName: "实验室助理",
  startDate: "2024-09-01",
  endDate: "2024-12-31"
};
const adminStats = await request.post('/api/workstudy/dashboard/student/stats/conditions', adminQuery);
```

## 性能优化

### 1. 数据库优化

**建议索引**：
```sql
-- 学生申请表索引
CREATE INDEX IDX_STUDENT_APPLY_XGH_XNXQ ON SYT_QGZX_STUDENT_APPLY(XGH, XNXQ);
CREATE INDEX IDX_STUDENT_APPLY_JOB_ID ON SYT_QGZX_STUDENT_APPLY(JOB_ID);

-- 考勤记录表索引
CREATE INDEX IDX_ATTENDANCE_RECORD_DATE ON SYT_QGZX_ATTENDANCE_RECORD(ATTENDANCE_DATE);
CREATE INDEX IDX_ATTENDANCE_RECORD_STUDENT_APPLY_ID ON SYT_QGZX_ATTENDANCE_RECORD(STUDENT_APPLY_ID);

-- 岗位类别表索引
CREATE INDEX IDX_JOB_TYPE_NAME ON SYT_QGZX_JOB_TYPE(NAME);
```

### 2. 查询优化

- ✅ 使用动态SQL避免不必要的JOIN
- ✅ 条件查询使用参数化查询防止SQL注入
- ✅ 时间范围查询使用Oracle日期函数优化
- ✅ 合理使用EXISTS和NOT EXISTS子查询

## 版本历史

- **v1.1.0** (2025-08-08)：多条件查询功能增强
  - 新增学年学期、岗位ID、岗位类别查询条件
  - 新增时间范围查询支持
  - 新增3个API接口
  - 完善的参数验证和异常处理
  - 遵循项目代码规范和字段命名约定
