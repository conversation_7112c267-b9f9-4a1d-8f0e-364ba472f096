[2m2025-08-09 09:58:01.272[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-09 09:58:01.528[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 37732 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-09 09:58:01.529[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-09 09:58:01.530[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-09 09:58:03.082[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-09 09:58:03.084[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-09 09:58:03.372[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-09 09:58:03.504[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 405 ms. Found 27 JPA repository interfaces.
[2m2025-08-09 09:58:03.546[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-09 09:58:03.546[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.581[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.582[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.582[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.582[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.584[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.585[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.586[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-09 09:58:03.612[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 65 ms. Found 1 MongoDB repository interface.
[2m2025-08-09 09:58:03.627[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-09 09:58:03.628[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-09 09:58:03.669[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.669[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.669[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.669[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.670[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.671[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-09 09:58:03.672[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
[2m2025-08-09 09:58:04.104[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-09 09:58:04.693[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-09 09:58:04.715[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-09 09:58:04.716[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-09 09:58:04.717[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-09 09:58:04.779[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-09 09:58:04.779[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3186 ms
[2m2025-08-09 09:58:04.962[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.6"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@31e08c61, com.mongodb.Jep395RecordCodecProvider@69eb88a7, com.mongodb.KotlinCodecProvider@2db15764]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-09 09:58:05.130[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=104018042, minRoundTripTimeNanos=0}
[2m2025-08-09 09:58:05.330[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-09 09:58:05.555[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-09 09:58:05.560[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@26ae9861'
[2m2025-08-09 09:58:05.560[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@993bc47'
[2m2025-08-09 09:58:05.560[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@db372b'
[2m2025-08-09 09:58:05.743[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-09 09:58:05.793[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-09 09:58:05.825[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-09 09:58:05.865[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-09 09:58:05.901[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-09 09:58:05.940[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-09 09:58:06.013[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-09 09:58:06.062[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-09 09:58:06.109[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-09 09:58:06.149[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-09 09:58:06.168[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-09 09:58:06.248[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-09 09:58:06.281[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-09 09:58:06.311[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-09 09:58:06.341[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-09 09:58:06.372[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-09 09:58:06.402[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-09 09:58:06.425[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-09 09:58:06.450[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-09 09:58:06.471[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-09 09:58:06.489[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-09 09:58:06.507[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-09 09:58:06.528[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-09 09:58:06.546[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-09 09:58:06.565[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-09 09:58:06.585[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-09 09:58:06.603[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-09 09:58:06.620[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-09 09:58:06.637[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-09 09:58:06.659[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-09 09:58:06.676[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-09 09:58:06.695[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-09 09:58:06.721[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-09 09:58:06.744[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-09 09:58:06.773[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-09 09:58:06.788[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-09 09:58:06.805[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-09 09:58:06.825[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-09 09:58:06.842[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-09 09:58:06.858[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-09 09:58:06.885[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-09 09:58:06.902[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-09 09:58:06.926[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-09 09:58:06.947[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-09 09:58:06.965[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-09 09:58:06.983[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-09 09:58:07.003[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-09 09:58:07.021[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-09 09:58:07.041[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-09 09:58:07.060[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-09 09:58:07.087[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-09 09:58:07.103[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-09 09:58:07.126[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-09 09:58:07.151[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-09 09:58:07.171[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-09 09:58:07.185[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-09 09:58:07.204[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-09 09:58:07.218[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-09 09:58:07.221[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-09 09:58:07.238[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-09 09:58:07.258[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-09 09:58:07.281[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-09 09:58:07.298[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-09 09:58:07.318[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-09 09:58:07.334[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-09 09:58:07.354[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-09 09:58:07.378[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-09 09:58:07.402[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-09 09:58:07.430[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-09 09:58:07.461[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-09 09:58:07.480[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-09 09:58:07.499[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-09 09:58:07.521[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-09 09:58:07.543[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-09 09:58:07.582[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-09 09:58:07.602[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-09 09:58:07.642[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-09 09:58:07.676[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-09 09:58:07.699[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-09 09:58:07.718[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-09 09:58:07.735[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-09 09:58:07.751[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-09 09:58:07.768[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-09 09:58:07.784[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-09 09:58:07.803[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-09 09:58:07.819[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-09 09:58:07.848[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-09 09:58:07.869[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-09 09:58:07.884[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-09 09:58:07.902[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-09 09:58:07.922[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-09 09:58:07.937[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-09 09:58:07.957[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-09 09:58:07.970[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-09 09:58:07.989[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-09 09:58:08.007[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-09 09:58:08.026[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-09 09:58:08.040[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-09 09:58:08.058[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-09 09:58:08.079[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-09 09:58:08.096[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-09 09:58:08.111[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-09 09:58:08.125[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-09 09:58:08.153[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-09 09:58:08.171[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-09 09:58:08.188[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-09 09:58:08.204[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-09 09:58:08.224[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-09 09:58:08.239[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-09 09:58:08.260[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-09 09:58:08.285[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-09 09:58:08.302[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-09 09:58:08.320[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-09 09:58:08.337[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-09 09:58:08.354[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-09 09:58:08.369[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-09 09:58:08.386[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-09 09:58:08.405[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-09 09:58:08.431[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-09 09:58:08.462[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-09 09:58:08.501[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-09 09:58:08.527[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-09 09:58:08.568[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-09 09:58:08.593[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-09 09:58:08.612[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-09 09:58:08.627[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-09 09:58:08.645[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-09 09:58:08.663[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-09 09:58:08.685[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-09 09:58:08.712[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-09 09:58:08.736[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-09 09:58:08.757[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-09 09:58:08.771[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-09 09:58:08.787[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-09 09:58:08.801[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-09 09:58:08.816[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-09 09:58:08.832[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-09 09:58:08.847[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-09 09:58:08.893[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-09 09:58:08.914[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-09 09:58:08.942[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-09 09:58:08.964[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-09 09:58:08.986[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-09 09:58:09.017[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-09 09:58:09.046[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-09 09:58:09.066[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-09 09:58:09.084[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-09 09:58:09.102[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-09 09:58:09.118[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-09 09:58:09.120[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowDashboardMapper.xml]'
[2m2025-08-09 09:58:09.138[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-09 09:58:09.154[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-09 09:58:09.170[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-09 09:58:09.188[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-09 09:58:09.206[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-09 09:58:09.231[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-09 09:58:09.239[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxDashboardMapper.xml]'
[2m2025-08-09 09:58:09.294[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-09 09:58:09.331[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-09 09:58:09.358[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-09 09:58:09.383[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-09 09:58:09.406[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-09 09:58:09.428[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:17 workerId:12
[2m2025-08-09 09:58:09.880[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-09 09:58:09.980[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-09 09:58:10.395[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-09 09:58:14.263[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-09 09:58:14.540[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-09 09:58:14.829[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-09 09:58:14.886[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-09 09:58:14.945[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-09 09:58:15.221[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-09 09:58:15.372[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-09 09:58:16.902[0;39m [33m WARN[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-09 09:58:17.152[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-09 09:58:05",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1369289977, ConnectTime:"2025-08-09 09:58:16", UseCount:1, LastActiveTime:"2025-08-09 09:58:17"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-09 09:58:20.173[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-09 09:58:20.190[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-09 09:58:20.715[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-09 09:58:20.715[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-09 09:58:30.689[0;39m [33m WARN[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 48c25e3b-d8f5-4dd1-973b-d07cb4ffc0f3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-09 09:58:30.704[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-09 09:58:31.286[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 48c25e3b-d8f5-4dd1-973b-d07cb4ffc0f3

[2m2025-08-09 09:58:31.567[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-09 09:58:31.612[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-09 09:58:31.631[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 31.083 seconds (process running for 32.866)
[2m2025-08-09 09:58:31.635[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-09 09:58:31.636[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-09 09:58:33.761[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-09 09:58:33.806[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-09 09:58:33.811[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-09 09:58:33.825[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-09 09:58:33.860[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-09 09:58:33.944[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:08.098[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-09 09:59:08.099[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-09 09:59:08.118[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 19 ms
[2m2025-08-09 09:59:08.235[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 09:59:08.248[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 09:59:08.250[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 09:59:08.252[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 09:59:08.253[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-09 09:59:08.325[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:08.897[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 09:59:08.914[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 09:59:08.916[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 09:59:08.917[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 09:59:08.917[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xh006(String), xh006(String)
[2m2025-08-09 09:59:09.231[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:09.238[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 09:59:09.251[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 09:59:09.254[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 09:59:09.254[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 09:59:09.254[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 019c96da420aba1d4925a9090357af14(String)
[2m2025-08-09 09:59:09.321[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-09 09:59:09.978[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
[2m2025-08-09 09:59:09.986[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
[2m2025-08-09 09:59:09.988[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ?
[2m2025-08-09 09:59:09.988[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ?
[2m2025-08-09 09:59:09.988[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.062[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.063[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
[2m2025-08-09 09:59:10.072[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
[2m2025-08-09 09:59:10.074[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi'
[2m2025-08-09 09:59:10.075[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi'
[2m2025-08-09 09:59:10.075[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.144[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.148[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
[2m2025-08-09 09:59:10.163[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
[2m2025-08-09 09:59:10.166[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG')
[2m2025-08-09 09:59:10.167[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG')
[2m2025-08-09 09:59:10.167[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.242[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.242[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
[2m2025-08-09 09:59:10.254[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
[2m2025-08-09 09:59:10.257[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID)
[2m2025-08-09 09:59:10.258[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID)
[2m2025-08-09 09:59:10.258[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.328[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.329[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
[2m2025-08-09 09:59:10.333[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
[2m2025-08-09 09:59:10.335[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG'
[2m2025-08-09 09:59:10.335[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG'
[2m2025-08-09 09:59:10.335[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.401[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.402[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
[2m2025-08-09 09:59:10.408[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
[2m2025-08-09 09:59:10.409[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS'
[2m2025-08-09 09:59:10.409[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS'
[2m2025-08-09 09:59:10.410[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.481[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.482[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 09:59:10.492[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 09:59:10.494[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 09:59:10.495[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 09:59:10.495[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.566[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 09:59:10.567[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
[2m2025-08-09 09:59:10.573[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
[2m2025-08-09 09:59:10.573[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo'
[2m2025-08-09 09:59:10.575[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==>  Preparing: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo'
[2m2025-08-09 09:59:10.575[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==> Parameters: xh006(String)
[2m2025-08-09 09:59:10.642[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:25.822[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:02:25.833[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:02:25.833[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:02:25.911[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:02:25.912[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-09 10:02:25.981[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:26.419[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:26.438[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:26.442[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:26.442[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:26.443[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-09 10:02:26.513[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:26.515[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:02:26.533[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:02:26.536[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:02:26.536[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:02:26.538[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-09 10:02:26.610[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-09 10:02:27.150[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.167[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.171[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.176[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.179[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.259[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.260[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.265[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.267[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.267[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.268[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.350[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.351[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.358[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.360[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG') AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.361[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG') AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.361[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.442[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.443[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.452[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.454[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID) AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.455[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID) AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.455[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.542[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.543[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.549[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.551[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.551[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.552[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.620[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.621[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.626[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.627[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.628[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS' AND sa.XNXQ = ?
[2m2025-08-09 10:02:27.628[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:27.946[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:27.951[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:27.969[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:27.971[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:27.971[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:27.972[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:28.058[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:28.060[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:28.078[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:28.080[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo' AND sa.XNXQ = ?
[2m2025-08-09 10:02:28.080[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==>  Preparing: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo' AND sa.XNXQ = ?
[2m2025-08-09 10:02:28.081[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==> Parameters: xh006(String), 2025(String)
[2m2025-08-09 10:02:28.174[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:39.497[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:02:39.504[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:02:39.506[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:02:39.506[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:02:39.507[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-09 10:02:39.584[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:39.891[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:39.918[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:39.918[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:39.926[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:02:39.926[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-09 10:02:39.993[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:39.994[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:02:40.026[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:02:40.027[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:02:40.027[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:02:40.028[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-09 10:02:40.106[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-09 10:02:40.661[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.685[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.686[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.687[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.694[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:40.763[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAppliedJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:40.764[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.786[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.789[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi' AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.790[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_INTERVIEW_RECORD ir INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND ir.INTERVIEW_RESULT = 'DaiMianShi' AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.790[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:40.859[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.s.p.w.m.Q.countStudentPendingInterviews[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:40.861[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.884[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.885[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG') AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.885[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.SPZT = 'TongGuo' AND sa.YGZT IN ('DSG', 'YG') AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.886[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:40.961[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAcceptedJobs    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:40.963[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.992[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.994[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID) AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.994[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND ja.SFQD = 1 AND NOT EXISTS (SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar WHERE ar.STUDENT_APPLY_ID = sa.ID) AND sa.XNXQ = ?
[2m2025-08-09 10:02:40.995[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:41.059[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentPendingCheckIn  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:41.060[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.079[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YG'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.080[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.080[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YG' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.081[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:41.154[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkingJobs     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:41.156[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.177[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            AND sa.YGZT = 'YGJS'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.180[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.181[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY sa INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.YGZT = 'YGJS' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.181[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:41.246[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentCompletedJobs   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:41.247[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:41.267[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
         
         WHERE sa.XGH = ?
            
                AND sa.XNXQ = ?
            
            
            
            
            
            
            
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:41.273[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:41.273[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) FROM SYT_QGZX_ATTENDANCE_RECORD ar INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID WHERE sa.XGH = ? AND sa.XNXQ = ? AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM') AND ar.ATTENDANCE_DATE < ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
[2m2025-08-09 10:02:41.274[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:41.347[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentAttendance      [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:02:41.348[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.371[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0)
        FROM SYT_QGZX_REMUNERATION_DETAIL rd
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID
         
         WHERE sa.XGH = ?
            AND ra.SPZT = 'TongGuo'
            
                AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.376[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.376[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==>  Preparing: SELECT NVL(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON rd.STUDENT_APPLY_ID = sa.ID INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID WHERE sa.XGH = ? AND ra.SPZT = 'TongGuo' AND sa.XNXQ = ?
[2m2025-08-09 10:02:41.376[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m ==> Parameters: xh005(String), 2025(String)
[2m2025-08-09 10:02:41.444[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.Q.countStudentWorkHours       [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:03:43.752[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:03:43.765[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:03:43.768[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:03:43.846[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:03:43.851[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 用人单位(String)
[2m2025-08-09 10:03:43.922[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:03:44.253[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:44.271[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:44.273[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:44.274[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:44.274[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: xy001(String), xy001(String)
[2m2025-08-09 10:03:44.343[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:03:44.345[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:03:44.356[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:03:44.362[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:03:44.363[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:03:44.363[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 760760c056ae97081e9a7456a7ef19ba(String)
[2m2025-08-09 10:03:44.430[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-09 10:03:44.986[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m Required request body is missing: public com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam)

org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:179)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:150)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:227)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:181)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:03:47.896[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:03:47.917[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:03:47.919[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:03:47.919[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:03:47.919[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-08-09 10:03:47.990[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:03:48.322[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:48.345[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:48.349[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:48.349[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:03:48.349[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-09 10:03:48.417[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:03:48.418[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:03:48.439[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:03:48.443[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:03:48.444[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:03:48.444[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-09 10:03:48.516[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-09 10:03:49.314[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.c.c.handler.GlobalExceptionHandler  [0;39m [2m:[0;39m Required request body is missing: public com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam)

org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:179)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:150)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:227)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:181)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:04:18.983[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:04:18.999[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:04:18.999[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:04:19.002[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:04:19.003[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-08-09 10:04:19.093[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:19.669[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:19.688[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:19.690[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:19.690[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:19.690[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-09 10:04:19.762[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:19.763[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:04:19.780[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:04:19.780[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:04:19.786[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:04:19.786[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-09 10:04:19.855[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-09 10:04:20.652[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:20.667[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:20.670[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:20.670[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==>  Preparing: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:20.671[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-09 10:04:20.754[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:20.755[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.s.i.WorkflowDashboardServiceImpl[0;39m [2m:[0;39m 按条件获取工作流统计数据失败，查询条件: WorkflowStatsQueryParam(year=null, projectId=null, moduleCode=null, startTime=null, endTime=null)

java.lang.NullPointerException: Cannot invoke "java.util.Map.getOrDefault(Object, Object)" because "workflowStats" is null
	at com.sanythadmin.project.workflow.service.impl.WorkflowDashboardServiceImpl.getWorkflowStatsByConditions(WorkflowDashboardServiceImpl.java:28)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(WorkflowDashboardController.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController$$SpringCGLIB$$0.getWorkflowStatsByQuery(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:04:26.901[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:04:26.908[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:04:26.909[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:04:26.910[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:04:26.910[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-08-09 10:04:26.977[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:27.304[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:27.313[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:27.313[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:27.315[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:04:27.315[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-09 10:04:27.381[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:27.381[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:04:27.387[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:04:27.389[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:04:27.390[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:04:27.390[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-09 10:04:27.455[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-09 10:04:28.178[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:28.191[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:28.193[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:28.194[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==>  Preparing: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
[2m2025-08-09 10:04:28.194[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-09 10:04:28.260[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:04:28.260[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.w.s.i.WorkflowDashboardServiceImpl[0;39m [2m:[0;39m 按条件获取工作流统计数据失败，查询条件: WorkflowStatsQueryParam(year=null, projectId=null, moduleCode=null, startTime=null, endTime=null)

java.lang.NullPointerException: Cannot invoke "java.util.Map.getOrDefault(Object, Object)" because "workflowStats" is null
	at com.sanythadmin.project.workflow.service.impl.WorkflowDashboardServiceImpl.getWorkflowStatsByConditions(WorkflowDashboardServiceImpl.java:28)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(WorkflowDashboardController.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController$$SpringCGLIB$$0.getWorkflowStatsByQuery(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:07:01.421[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:07:01.437[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:07:01.438[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:07:01.508[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:07:01.510[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-08-09 10:07:01.585[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:07:01.907[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:07:01.964[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:07:01.965[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:07:01.965[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:07:02.081[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-09 10:07:02.151[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:07:02.152[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:07:02.176[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:07:02.215[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:07:02.216[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:07:02.216[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-09 10:07:02.281[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-09 10:07:03.124[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
         WHERE  w.YEAR = ?
            
            
            
                AND w.MODULE_CODE = ?
[2m2025-08-09 10:07:03.161[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
         WHERE  w.YEAR = ?
            
            
            
                AND w.MODULE_CODE = ?
[2m2025-08-09 10:07:03.168[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID WHERE w.YEAR = ? AND w.MODULE_CODE = ?
[2m2025-08-09 10:07:03.168[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==>  Preparing: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID WHERE w.YEAR = ? AND w.MODULE_CODE = ?
[2m2025-08-09 10:07:03.169[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==> Parameters: 2025(String), qgzxGwsb(String)
[2m2025-08-09 10:07:03.250[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:07:03.253[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.s.i.WorkflowDashboardServiceImpl[0;39m [2m:[0;39m 按条件获取工作流统计数据失败，查询条件: WorkflowStatsQueryParam(year=2025, projectId=, moduleCode=qgzxGwsb, startTime=null, endTime=null)

java.lang.NullPointerException: Cannot invoke "java.util.Map.getOrDefault(Object, Object)" because "workflowStats" is null
	at com.sanythadmin.project.workflow.service.impl.WorkflowDashboardServiceImpl.getWorkflowStatsByConditions(WorkflowDashboardServiceImpl.java:28)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(WorkflowDashboardController.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController$$SpringCGLIB$$0.getWorkflowStatsByQuery(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:08:30.430[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:08:30.446[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,TYPE,ROLE_SCOPE,remark    FROM  SYT_SYS_ROLE         WHERE  (NAME = ?)
[2m2025-08-09 10:08:30.448[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:08:30.517[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, TYPE, ROLE_SCOPE, remark FROM SYT_SYS_ROLE WHERE (NAME = ?)
[2m2025-08-09 10:08:30.518[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生处(String)
[2m2025-08-09 10:08:30.593[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.mapper.SysRoleMapper.selectList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:08:30.892[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:08:30.898[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:08:30.898[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:08:30.901[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-09 10:08:30.901[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-09 10:08:30.968[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:08:30.968[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:08:30.973[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-09 10:08:30.975[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:08:30.976[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-09 10:08:30.976[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-09 10:08:31.040[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-09 10:08:31.913[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
         WHERE  w.YEAR = ?
            
            
            
                AND w.MODULE_CODE = ?
[2m2025-08-09 10:08:31.935[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT
            SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
         WHERE  w.YEAR = ?
            
            
            
                AND w.MODULE_CODE = ?
[2m2025-08-09 10:08:31.937[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID WHERE w.YEAR = ? AND w.MODULE_CODE = ?
[2m2025-08-09 10:08:31.937[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==>  Preparing: SELECT SUM(CASE WHEN wr.RESULT = 'DaiShenPi' THEN 1 ELSE 0 END) AS pendingApprovals, SUM(CASE WHEN wr.RESULT = 'TongGuo' THEN 1 ELSE 0 END) AS approved, SUM(CASE WHEN wr.RESULT = 'BuTongGuo' THEN 1 ELSE 0 END) AS rejected, SUM(CASE WHEN wr.RESULT = 'TuiHui' THEN 1 ELSE 0 END) AS returned FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID WHERE w.YEAR = ? AND w.MODULE_CODE = ?
[2m2025-08-09 10:08:31.938[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m ==> Parameters: 2025(String), qgzxGwsb(String)
[2m2025-08-09 10:08:32.013[0;39m [32mDEBUG[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36ms.p.w.m.W.countWorkflowStatsByConditions[0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-09 10:08:32.014[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.s.i.WorkflowDashboardServiceImpl[0;39m [2m:[0;39m 按条件获取工作流统计数据失败，查询条件: WorkflowStatsQueryParam(year=2025, projectId=, moduleCode=qgzxGwsb, startTime=null, endTime=null)

java.lang.NullPointerException: Cannot invoke "java.util.Map.getOrDefault(Object, Object)" because "workflowStats" is null
	at com.sanythadmin.project.workflow.service.impl.WorkflowDashboardServiceImpl.getWorkflowStatsByConditions(WorkflowDashboardServiceImpl.java:28)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController.getWorkflowStatsByQuery(WorkflowDashboardController.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.sanythadmin.project.workflow.controller.WorkflowDashboardController$$SpringCGLIB$$0.getWorkflowStatsByQuery(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.sanythadmin.common.core.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:110)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 10:16:38.608[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 10:16:38.749[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=63002666, minRoundTripTimeNanos=0}
[2m2025-08-09 10:18:13.388[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 10:18:13.888[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=171728792, minRoundTripTimeNanos=0}
[2m2025-08-09 10:25:49.362[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 10:25:49.554[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=61233791, minRoundTripTimeNanos=0}
[2m2025-08-09 10:47:04.229[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 10:47:04.402[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=86796166, minRoundTripTimeNanos=0}
[2m2025-08-09 11:00:24.996[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 11:00:25.138[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=68011625, minRoundTripTimeNanos=0}
[2m2025-08-09 11:10:13.501[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 11:10:13.655[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=66374208, minRoundTripTimeNanos=0}
[2m2025-08-09 11:39:10.696[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 11:39:10.866[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=74670542, minRoundTripTimeNanos=0}
[2m2025-08-09 12:12:13.225[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 12:12:14.388[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=68249084, minRoundTripTimeNanos=0}
[2m2025-08-09 12:29:40.364[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 12:29:40.515[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=66780875, minRoundTripTimeNanos=0}
[2m2025-08-09 12:40:30.261[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 12:40:30.429[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=70817042, minRoundTripTimeNanos=0}
[2m2025-08-09 12:40:45.906[0;39m [31mERROR[0;39m [35m37732[0;39m [2m---[0;39m [2m[isson-timer-4-1][0;39m [36mo.r.c.handler.PingConnectionHandler     [0;39m [2m:[0;39m Unable to send PING command over channel: [id: 0x39d5f7a4, L:/************:63097 - R:*************/*************:6380]

org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:6380]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:787)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)

[2m2025-08-09 13:15:52.541[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 13:15:52.712[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=77302333, minRoundTripTimeNanos=0}
[2m2025-08-09 13:26:55.070[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 13:26:55.235[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=75457458, minRoundTripTimeNanos=0}
[2m2025-08-09 13:37:10.256[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server *************:37017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-08-09 13:37:10.438[0;39m [32m INFO[0;39m [35m37732[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=66641791, minRoundTripTimeNanos=0}
