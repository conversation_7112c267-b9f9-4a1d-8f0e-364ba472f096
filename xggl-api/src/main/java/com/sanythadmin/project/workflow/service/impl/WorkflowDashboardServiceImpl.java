package com.sanythadmin.project.workflow.service.impl;

import com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO;
import com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam;
import com.sanythadmin.project.workflow.mapper.WorkflowDashboardMapper;
import com.sanythadmin.project.workflow.service.WorkflowDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowDashboardServiceImpl implements WorkflowDashboardService {

    private final WorkflowDashboardMapper workflowDashboardMapper;

    @Override
    public WorkflowDashboardStatsDTO getWorkflowStatsByConditions(WorkflowStatsQueryParam queryDTO) {
        try {
            Map<String, Long> workflowStats = workflowDashboardMapper.countWorkflowStatsByConditions(
                queryDTO.getYear(), queryDTO.getProjectId(), queryDTO.getModuleCode(),
                queryDTO.getStartTime(), queryDTO.getEndTime()
            );

            Long pendingApprovals = workflowStats.getOrDefault("pendingApprovals", 0L);
            Long approved = workflowStats.getOrDefault("approved", 0L);
            Long rejected = workflowStats.getOrDefault("rejected", 0L);
            Long returned = workflowStats.getOrDefault("returned", 0L);
            WorkflowDashboardStatsDTO stats = new WorkflowDashboardStatsDTO(
                pendingApprovals, approved, rejected, returned
            );
            return stats;
        } catch (Exception e) {
            log.error("按条件获取工作流统计数据失败，查询条件: {}", queryDTO, e);
            return new WorkflowDashboardStatsDTO();
        }
    }

}
