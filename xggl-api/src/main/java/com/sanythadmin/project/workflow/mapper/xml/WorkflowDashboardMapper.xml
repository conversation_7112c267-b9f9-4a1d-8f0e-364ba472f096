<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowDashboardMapper">

    <select id="countWorkflowStatsByConditions" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
            SUM(CASE WHEN wr.RESULT = '待审批' THEN 1 ELSE 0 END) as pendingApprovals,
            SUM(CASE WHEN wr.RESULT = '通过' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN wr.RESULT = '不通过' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN wr.RESULT = '退回' THEN 1 ELSE 0 END) as returned
        FROM SYT_WORKFLOW_APPROVAL_NODE_RECORD wr
        INNER JOIN SYT_WORKFLOW_APPROVAL_NODE wn ON wr.NODE_ID = wn.ID
        INNER JOIN SYT_WORKFLOW w ON wn.WORKFLOW_ID = w.ID
        <where>
            <if test="year != null and year != ''">
                AND w.YEAR = #{year}
            </if>
            <if test="projectId != null and projectId != ''">
                AND w.PROJECT_ID = #{projectId}
            </if>
            <if test="moduleCode != null and moduleCode != ''">
                AND w.MODULE_CODE = #{moduleCode}
            </if>
            <if test="startTime != null and startTime != ''">
                AND wr.CREATE_TIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime != null and endTime != ''">
                AND wr.CREATE_TIME &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
        </where>
    </select>

</mapper>
