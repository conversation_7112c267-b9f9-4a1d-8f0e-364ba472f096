package com.sanythadmin.project.workflow.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.workflow.dto.WorkflowDashboardStatsDTO;
import com.sanythadmin.project.workflow.param.WorkflowStatsQueryParam;
import com.sanythadmin.project.workflow.service.WorkflowDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流管理模块/工作流统计控制器
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@RestController
@RequestMapping("/api/workflow/dashboard")
@RequiredArgsConstructor
public class WorkflowDashboardController extends BaseController {

    private final WorkflowDashboardService workflowDashboardService;

    /**
     * 工作流统计数据（权限标识：workflow:dashboard:stats）
     */
    @OperationLog(module = "工作流统计", comments = "工作流统计数据")
    @PreAuthorize("hasAuthority('workflow:dashboard:stats')")
    @GetMapping("/stats/query")
    public WorkflowDashboardStatsDTO getWorkflowStatsByQuery(@RequestBody WorkflowStatsQueryParam queryDTO) {
        return workflowDashboardService.getWorkflowStatsByConditions(queryDTO);
    }

}
