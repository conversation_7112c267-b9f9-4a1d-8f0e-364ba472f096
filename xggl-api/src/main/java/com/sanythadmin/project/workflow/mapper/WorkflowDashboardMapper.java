package com.sanythadmin.project.workflow.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通用工作流统计数据Mapper
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Mapper
public interface WorkflowDashboardMapper {

    Map<String, Long> countWorkflowStatsByConditions(@Param("year") String year,
                                                     @Param("projectId") String projectId,
                                                     @Param("moduleCode") String moduleCode,
                                                     @Param("startTime") String startTime,
                                                     @Param("endTime") String endTime);
}
