package com.sanythadmin.project.workflow.dto;

import lombok.Data;

/**
 * 通用工作流统计数据DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
public class WorkflowDashboardStatsDTO {

    /**
     * 待审批数
     */
    private Long pendingApprovals;

    /**
     * 通过数
     */
    private Long approved;

    /**
     * 不通过数
     */
    private Long rejected;

    /**
     * 退回数
     */
    private Long returned;

    /**
     * 默认构造函数
     * 初始化所有统计数据为0
     */
    public WorkflowDashboardStatsDTO() {
        this.pendingApprovals = 0L;
        this.approved = 0L;
        this.rejected = 0L;
        this.returned = 0L;
    }

    /**
     * 带参构造函数
     *
     * @param pendingApprovals 待审批数
     * @param approved 通过数
     * @param rejected 不通过数
     * @param returned 退回数
     */
    public WorkflowDashboardStatsDTO(Long pendingApprovals, Long approved, <PERSON> rejected, <PERSON> returned) {
        this.pendingApprovals = pendingApprovals != null ? pendingApprovals : 0L;
        this.approved = approved != null ? approved : 0L;
        this.rejected = rejected != null ? rejected : 0L;
        this.returned = returned != null ? returned : 0L;
    }
}
