package com.sanythadmin.project.workstudy.service.impl;

import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.workstudy.dto.QgzxStudentPersonalStatsDTO;
import com.sanythadmin.project.workstudy.dto.QgzxStudentStatsQueryDTO;
import com.sanythadmin.project.workstudy.mapper.QgzxDashboardMapper;
import com.sanythadmin.project.workstudy.service.QgzxDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 勤工助学统计服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxDashboardServiceImpl implements QgzxDashboardService {

    private final QgzxDashboardMapper dashboardMapper;

    @Override
    public QgzxStudentPersonalStatsDTO getStudentPersonalStats(QgzxStudentStatsQueryDTO queryDTO) {
        try {
            if (queryDTO == null || queryDTO.getXgh() == null || queryDTO.getXgh().trim().isEmpty()) {
                log.warn("查询条件为空或学号为空，返回默认统计数据");
                return new QgzxStudentPersonalStatsDTO();
            }
            SysAccount account = SecurityUtil.getAccount();
            String studentName = account != null ? account.getRealName() : "";
            String studentNumber = account != null ? account.getUsername() : "";
            Long myAppliedJobs = dashboardMapper.countStudentAppliedJobs(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long myPendingInterviews = dashboardMapper.countStudentPendingInterviews(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long myAcceptedJobs = dashboardMapper.countStudentAcceptedJobs(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long myPendingCheckIn = dashboardMapper.countStudentPendingCheckIn(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long myWorkingJobs = dashboardMapper.countStudentWorkingJobs(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long myCompletedJobs = dashboardMapper.countStudentCompletedJobs(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName());
            Long monthlyAttendance = dashboardMapper.countStudentAttendance(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName(),
                queryDTO.getStartDate(), queryDTO.getEndDate());
            Double totalWorkHours = dashboardMapper.countStudentWorkHours(
                queryDTO.getXgh(), queryDTO.getXnxq(), queryDTO.getJobId(), queryDTO.getJobTypeName(),
                queryDTO.getStartDate(), queryDTO.getEndDate());

            QgzxStudentPersonalStatsDTO stats = new QgzxStudentPersonalStatsDTO(
                queryDTO.getXgh(), studentName, studentNumber,
                myAppliedJobs, myPendingInterviews, myAcceptedJobs,
                myPendingCheckIn, myWorkingJobs, myCompletedJobs,
                monthlyAttendance, totalWorkHours
            );
            return stats;
        } catch (Exception e) {
            log.error("按条件获取学生个人统计数据失败，查询条件: {}", queryDTO, e);
            return new QgzxStudentPersonalStatsDTO();
        }
    }

}
