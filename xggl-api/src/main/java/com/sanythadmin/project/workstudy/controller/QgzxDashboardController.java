package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.workstudy.dto.QgzxStudentPersonalStatsDTO;
import com.sanythadmin.project.workstudy.dto.QgzxStudentStatsQueryDTO;
import com.sanythadmin.project.workstudy.service.QgzxDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 勤工助学/统计控制器
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/api/workstudy/dashboard")
@RequiredArgsConstructor
public class QgzxDashboardController extends BaseController {

    private final QgzxDashboardService dashboardService;
    
    /**
     * 当前学生个人勤工助学统计(权限标识：workstudy:student:personal-stats)
     */
    @OperationLog(module = "勤工助学统计", comments = "当前学生个人勤工助学统计")
    @PreAuthorize("hasAuthority('workstudy:student:personal-stats')")
    @PostMapping("/student/personal-stats")
    public QgzxStudentPersonalStatsDTO getCurrentStudentStats(@RequestBody QgzxStudentStatsQueryDTO queryDTO) {
        queryDTO.setXgh(SecurityUtil.getUsername());
        return dashboardService.getStudentPersonalStats(queryDTO);
    }

    /**
     * 指定学生个人勤工助学统计(权限标识：workstudy:admin:student-stats)
     */
    @OperationLog(module = "勤工助学统计", comments = "指定学生个人勤工助学统计")
    @PreAuthorize("hasAuthority('workstudy:admin:student-stats')")
    @PostMapping("/student/stats")
    public QgzxStudentPersonalStatsDTO getStudentStats(@RequestBody QgzxStudentStatsQueryDTO queryDTO) {
        return dashboardService.getStudentPersonalStats(queryDTO);
    }

}
