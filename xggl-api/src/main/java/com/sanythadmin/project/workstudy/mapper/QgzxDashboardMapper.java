package com.sanythadmin.project.workstudy.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 勤工助学统计数据Mapper
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Mapper
public interface QgzxDashboardMapper {


    /**
     * 按条件统计学生申请的岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 申请岗位数
     */
    Long countStudentAppliedJobs(@Param("xgh") String xgh,
                                               @Param("xnxq") String xnxq,
                                               @Param("jobId") String jobId,
                                               @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生待面试岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 待面试岗位数
     */
    Long countStudentPendingInterviews(@Param("xgh") String xgh,
                                                     @Param("xnxq") String xnxq,
                                                     @Param("jobId") String jobId,
                                                     @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生已录用岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 已录用岗位数
     */
    Long countStudentAcceptedJobs(@Param("xgh") String xgh,
                                                @Param("xnxq") String xnxq,
                                                @Param("jobId") String jobId,
                                                @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生待签到岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 待签到岗位数
     */
    Long countStudentPendingCheckIn(@Param("xgh") String xgh,
                                                  @Param("xnxq") String xnxq,
                                                  @Param("jobId") String jobId,
                                                  @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生工作中岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 工作中岗位数
     */
    Long countStudentWorkingJobs(@Param("xgh") String xgh,
                                               @Param("xnxq") String xnxq,
                                               @Param("jobId") String jobId,
                                               @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生已完成岗位数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @return 已完成岗位数
     */
    Long countStudentCompletedJobs(@Param("xgh") String xgh,
                                                 @Param("xnxq") String xnxq,
                                                 @Param("jobId") String jobId,
                                                 @Param("jobTypeName") String jobTypeName);

    /**
     * 按条件统计学生考勤次数
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 考勤次数
     */
    Long countStudentAttendance(@Param("xgh") String xgh,
                                              @Param("xnxq") String xnxq,
                                              @Param("jobId") String jobId,
                                              @Param("jobTypeName") String jobTypeName,
                                              @Param("startDate") String startDate,
                                              @Param("endDate") String endDate);

    /**
     * 按条件统计学生工作时长
     * 支持按学年学期、岗位ID、岗位类别进行筛选
     *
     * @param xgh 学号
     * @param xnxq 学年学期（可选）
     * @param jobId 岗位ID（可选）
     * @param jobTypeName 岗位类别名称（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 工作时长
     */
    Double countStudentWorkHours(@Param("xgh") String xgh,
                                               @Param("xnxq") String xnxq,
                                               @Param("jobId") String jobId,
                                               @Param("jobTypeName") String jobTypeName,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);
}
