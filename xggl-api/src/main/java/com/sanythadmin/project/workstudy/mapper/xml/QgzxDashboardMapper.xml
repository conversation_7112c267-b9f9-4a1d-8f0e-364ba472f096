<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxDashboardMapper">

    <!-- 统计学生申请的岗位数 -->
    <select id="countStudentAppliedJobs" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生待面试岗位数 -->
    <select id="countStudentPendingInterviews" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_INTERVIEW_RECORD ir
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ir.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            AND ir.INTERVIEW_RESULT = 'DaiMianShi'
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生已录用岗位数 -->
    <select id="countStudentAcceptedJobs" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            AND sa.SPZT = 'TongGuo'
            AND sa.YGZT IN ('DSG', 'YG')
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生待签到岗位数 -->
    <select id="countStudentPendingCheckIn" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            AND sa.YGZT = 'YG'
            AND ja.SFQD = 1
            AND NOT EXISTS (
                SELECT 1 FROM SYT_QGZX_ATTENDANCE_RECORD ar
                WHERE ar.STUDENT_APPLY_ID = sa.ID
            )
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生工作中岗位数 -->
    <select id="countStudentWorkingJobs" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            AND sa.YGZT = 'YG'
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生已完成岗位数 -->
    <select id="countStudentCompletedJobs" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_STUDENT_APPLY sa
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            AND sa.YGZT = 'YGJS'
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
        </where>
    </select>

    <!-- 统计学生考勤次数 -->
    <select id="countStudentAttendance" resultType="java.lang.Long" parameterType="java.util.Map">
        SELECT COUNT(*)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
            <if test="startDate != null and startDate != ''">
                AND ar.ATTENDANCE_DATE >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
            </if>
            <if test="endDate != null and endDate != ''">
                AND ar.ATTENDANCE_DATE &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')
            </if>
            <!-- 如果没有指定日期范围，默认查询本月 -->
            <if test="(startDate == null or startDate == '') and (endDate == null or endDate == '')">
                AND ar.ATTENDANCE_DATE >= TRUNC(SYSDATE, 'MM')
                AND ar.ATTENDANCE_DATE &lt; ADD_MONTHS(TRUNC(SYSDATE, 'MM'), 1)
            </if>
        </where>
    </select>

    <!-- 统计学生工作时长 -->
    <select id="countStudentWorkHours" resultType="java.lang.Double" parameterType="java.util.Map">
        SELECT NVL(SUM(ar.WORK_HOURS), 0)
        FROM SYT_QGZX_ATTENDANCE_RECORD ar
        INNER JOIN SYT_QGZX_STUDENT_APPLY sa ON ar.STUDENT_APPLY_ID = sa.ID
        INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON sa.JOB_ID = ja.ID
        <if test="jobTypeName != null and jobTypeName != ''">
            INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID
        </if>
        <where>
            sa.XGH = #{xgh}
            <if test="xnxq != null and xnxq != ''">
                AND sa.XNXQ = #{xnxq}
            </if>
            <if test="jobId != null and jobId != ''">
                AND sa.JOB_ID = #{jobId}
            </if>
            <if test="jobTypeName != null and jobTypeName != ''">
                AND jt.NAME = #{jobTypeName}
            </if>
            <if test="startDate != null and startDate != ''">
                AND ar.ATTENDANCE_DATE >= TO_DATE(#{startDate}, 'YYYY-MM-DD')
            </if>
            <if test="endDate != null and endDate != ''">
                AND ar.ATTENDANCE_DATE &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD')
            </if>
        </where>
    </select>

</mapper>
