package com.sanythadmin.project.workstudy.dto;

import lombok.Data;

/**
 * 学生个人勤工助学统计数据DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
public class QgzxStudentPersonalStatsDTO {

    /**
     * 学生基本信息
     */
    private String studentId;
    private String studentName;
    private String studentNumber;

    /**
     * 我申请的岗位数
     * 统计当前学生已提交申请的岗位总数
     */
    private Long myAppliedJobs;

    /**
     * 待面试岗位数
     * 统计当前学生申请的岗位中，面试结果为"待面试"状态的记录数
     */
    private Long myPendingInterviews;

    /**
     * 已录用岗位数
     * 统计当前学生申请的岗位中，已被录用的岗位数
     */
    private Long myAcceptedJobs;

    /**
     * 待签到岗位数
     * 统计当前学生已录用但还未开始签到的岗位数
     */
    private Long myPendingCheckIn;

    /**
     * 工作中岗位数
     * 统计当前学生正在工作中的岗位数
     */
    private Long myWorkingJobs;

    /**
     * 已完成岗位数
     * 统计当前学生已完成工作的岗位数
     */
    private Long myCompletedJobs;

    /**
     * 本月考勤次数
     * 统计当前学生本月的考勤签到次数
     */
    private Long monthlyAttendance;

    /**
     * 累计工作时长（小时）
     * 统计当前学生累计的工作时长
     */
    private Double totalWorkHours;

    public QgzxStudentPersonalStatsDTO() {
        this.myAppliedJobs = 0L;
        this.myPendingInterviews = 0L;
        this.myAcceptedJobs = 0L;
        this.myPendingCheckIn = 0L;
        this.myWorkingJobs = 0L;
        this.myCompletedJobs = 0L;
        this.monthlyAttendance = 0L;
        this.totalWorkHours = 0.0;
    }

    public QgzxStudentPersonalStatsDTO(String studentId, String studentName, String studentNumber,
                                       Long myAppliedJobs, Long myPendingInterviews, Long myAcceptedJobs,
                                       Long myPendingCheckIn, Long myWorkingJobs, Long myCompletedJobs,
                                       Long monthlyAttendance, Double totalWorkHours) {
        this.studentId = studentId;
        this.studentName = studentName;
        this.studentNumber = studentNumber;
        this.myAppliedJobs = myAppliedJobs != null ? myAppliedJobs : 0L;
        this.myPendingInterviews = myPendingInterviews != null ? myPendingInterviews : 0L;
        this.myAcceptedJobs = myAcceptedJobs != null ? myAcceptedJobs : 0L;
        this.myPendingCheckIn = myPendingCheckIn != null ? myPendingCheckIn : 0L;
        this.myWorkingJobs = myWorkingJobs != null ? myWorkingJobs : 0L;
        this.myCompletedJobs = myCompletedJobs != null ? myCompletedJobs : 0L;
        this.monthlyAttendance = monthlyAttendance != null ? monthlyAttendance : 0L;
        this.totalWorkHours = totalWorkHours != null ? totalWorkHours : 0.0;
    }
}
