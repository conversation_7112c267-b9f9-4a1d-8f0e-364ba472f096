package com.sanythadmin.project.entryexit.repository;

import com.sanythadmin.project.entryexit.entity.EntryExitTimeWindowAnomalyRule;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.stream.Stream;

public interface EntryExitTimeWindowAnomalyRuleRepository extends PagingAndSortingRepository<EntryExitTimeWindowAnomalyRule, String>, CrudRepository<EntryExitTimeWindowAnomalyRule, String>, JpaSpecificationExecutor<EntryExitTimeWindowAnomalyRule> {
    @Query("select e from EntryExitTimeWindowAnomalyRule e where e.project.id = :projectId and e.enabled = true")
    Stream<EntryExitTimeWindowAnomalyRule> listEnabledByProjectId(String projectId);
}
