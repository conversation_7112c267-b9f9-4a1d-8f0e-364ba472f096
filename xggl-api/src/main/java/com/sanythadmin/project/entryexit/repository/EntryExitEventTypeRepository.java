package com.sanythadmin.project.entryexit.repository;

import com.sanythadmin.project.entryexit.entity.EntryExitEventType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface EntryExitEventTypeRepository extends PagingAndSortingRepository<EntryExitEventType, String>, CrudRepository<EntryExitEventType, String>, JpaSpecificationExecutor<EntryExitEventType> {

    @Query("select e from EntryExitEventType e where e.project.id = :projectId order by e.sort")
    List<EntryExitEventType> listByProjectId(String projectId);
}
