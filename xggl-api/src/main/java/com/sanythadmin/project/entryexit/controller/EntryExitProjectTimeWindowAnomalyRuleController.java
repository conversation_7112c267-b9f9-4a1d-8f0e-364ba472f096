package com.sanythadmin.project.entryexit.controller;

import com.sanythadmin.common.core.utils.BusinessExceptionUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.entryexit.entity.EntryExitProject;
import com.sanythadmin.project.entryexit.entity.EntryExitTimeWindowAnomalyRule;
import com.sanythadmin.project.entryexit.enums.EntryExitSeverityLevel;
import com.sanythadmin.project.entryexit.form.EntryExitTimeWindowAnomalyRuleForm;
import com.sanythadmin.project.entryexit.query.EntryExitQuerySuper;
import com.sanythadmin.project.entryexit.repository.EntryExitTimeWindowAnomalyRuleRepository;
import com.sanythadmin.project.entryexit.vo.EntryExitIdTextPairVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 出入管理/项目管理/异常时间段规则
 *
 * @since 2025/8/5 15:47
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/entry-exit/project/time-window-anomaly-rule")
public class EntryExitProjectTimeWindowAnomalyRuleController {

    private final EntryExitTimeWindowAnomalyRuleRepository entityRepository;

    /**
     * 分页查询
     * 权限标识：entry-exit:project:time-window-anomaly-rule:list
     */
    @PreAuthorize("hasAuthority('entry-exit:project:time-window-anomaly-rule:list')")
    @GetMapping("/page")
    public PageResult<EntryExitTimeWindowAnomalyRuleForm> page(EntryExitQuerySuper query, @RequestParam String projectId) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<EntryExitTimeWindowAnomalyRule> page = entityRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder)
                        -> criteriaBuilder.equal(root.get("project").get("id"), projectId)
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(EntryExitTimeWindowAnomalyRuleForm::new).toList(), page.getTotalElements());
    }

    /**
     * 可选异常严重程度
     */
    @PreAuthorize("hasAuthority('entry-exit:project:time-window-anomaly-rule:list')")
    @GetMapping("/optionalSeverity")
    public List<EntryExitIdTextPairVo> optionalSeverity() {
        return Arrays.stream(EntryExitSeverityLevel.values()).map(item -> new EntryExitIdTextPairVo(item.name(), item.getText())).toList();
    }

    /**
     * 添加或修改
     * 权限标识：entry-exit:project:time-window-anomaly-rule:operation
     */
    @PreAuthorize("hasAuthority('entry-exit:project:time-window-anomaly-rule:operation')")
    @PostMapping("/operation")
    public void operation(@RequestBody EntryExitTimeWindowAnomalyRuleForm form) {
        EntryExitTimeWindowAnomalyRule entity;
        if (form.getId() != null) {
            entity = entityRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new EntryExitTimeWindowAnomalyRule();
            entity.setCreatedAt(LocalDateTime.now());

            BusinessExceptionUtil.checkTrue(form.getProjectId() != null, "projectId required");
            EntryExitProject project = new EntryExitProject();
            project.setId(form.getProjectId());
            entity.setProject(project);
        }

        BusinessExceptionUtil.checkTrue(StringUtils.isNotEmpty(form.getName()), "name required");
        BusinessExceptionUtil.checkTrue(form.getStartTimeOfDay() != null, "startTimeOfDay required");
        BusinessExceptionUtil.checkTrue(form.getEndTimeOfDay() != null, "endTimeOfDay required");
        BusinessExceptionUtil.checkTrue(form.getSeverity() != null, "severity required");

        entity.setName(form.getName());
        entity.setEnabled(form.getEnabled() == JudgeMark.YES);
        entity.setStartTimeOfDay(form.getStartTimeOfDay());
        entity.setEndTimeOfDay(form.getEndTimeOfDay());
        entity.setSeverity(form.getSeverity());

        entityRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：entry-exit:project:time-window-anomaly-rule:remove
     */
    @PreAuthorize("hasAuthority('entry-exit:project:time-window-anomaly-rule:remove')")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        entityRepository.deleteAllById(ids);
    }
}
