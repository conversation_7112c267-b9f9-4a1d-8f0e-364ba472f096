package com.sanythadmin.project.entryexit.vo;

import com.sanythadmin.project.entryexit.entity.EntryExitRecord;
import com.sanythadmin.project.entryexit.util.EntryExitUtil;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @since 2025/8/5 16:40
 */
@Data
public class EntryExitRecordVo {

    public EntryExitRecordVo(EntryExitRecord entity) {
        this.id = entity.getId();
        this.eventTypeCode = entity.getEventTypeCode();
        this.user = EntryExitUtil.userInfoTransform(entity.getUser());
        this.source = entity.getSource();
        this.campus = entity.getCampus();
        this.dateTime = entity.getDateTime();
    }

    private String id;
    /**
     * 出入类型
     */
    private String eventTypeCode;
    private EntryExitUserVo user;
    /**
     * 来源
     */
    private String source;
    /**
     * 校区
     */
    private String campus;
    private LocalDateTime dateTime;
}
