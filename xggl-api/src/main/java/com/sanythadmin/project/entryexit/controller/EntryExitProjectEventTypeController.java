package com.sanythadmin.project.entryexit.controller;

import com.sanythadmin.common.core.utils.BusinessExceptionUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.entryexit.entity.EntryExitEventType;
import com.sanythadmin.project.entryexit.entity.EntryExitProject;
import com.sanythadmin.project.entryexit.form.EntryExitEventTypeForm;
import com.sanythadmin.project.entryexit.query.EntryExitQuerySuper;
import com.sanythadmin.project.entryexit.repository.EntryExitEventTypeRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入管理/项目管理/出入类型
 *
 * @since 2025/8/5 14:46
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/entry-exit/project/event-type")
public class EntryExitProjectEventTypeController {

    private final EntryExitEventTypeRepository eventTypeRepository;

    /**
     * 分页查询
     * 权限标识：entry-exit:project:event-type:list
     */
    @PreAuthorize("hasAuthority('entry-exit:project:event-type:list')")
    @GetMapping("/page")
    public PageResult<EntryExitEventTypeForm> page(EntryExitQuerySuper query, @RequestParam String projectId) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<EntryExitEventType> page = eventTypeRepository.findAll(Specification.allOf(
                        (root, query1, criteriaBuilder)
                                -> criteriaBuilder.equal(root.get("project").get("id"), projectId)
                ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(EntryExitEventTypeForm::new).toList(), page.getTotalElements());
    }

    /**
     * 添加或修改
     * 权限标识：entry-exit:project:event-type:operation
     */
    @PreAuthorize("hasAuthority('entry-exit:project:event-type:operation')")
    @PostMapping("/operation")
    public void operation(@RequestBody EntryExitEventTypeForm form) {
        EntryExitEventType entity;
        if (form.getId() != null) {
            entity = eventTypeRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new EntryExitEventType();
            entity.setCreatedAt(LocalDateTime.now());

            BusinessExceptionUtil.checkTrue(form.getProjectId() != null, "projectId required");
            EntryExitProject project = new EntryExitProject();
            project.setId(form.getProjectId());
            entity.setProject(project);
        }

        entity.setName(form.getName());
        entity.setCode(form.getCode());
        entity.setSort(form.getSort());

        eventTypeRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：entry-exit:project:event-type:remove
     */
    @PreAuthorize("hasAuthority('entry-exit:project:event-type:remove')")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        eventTypeRepository.deleteAllById(ids);
    }
}
