<template>
  <component :is="iconComponent" v-bind="iconProps" style="cursor: pointer;"/>
</template>

<script setup>
import {computed} from 'vue';
import * as IconPark from '@icon-park/vue-next';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  size: {
    type: [Number, String],
    default: 24,
  },
  color: {
    type: String,
    // default: 'var(--el-color-primary)',
  },
  theme: {
    type: String,
    default: 'outline',//线性（outline）、填充（filled）、双色（two-tone）、多色（multi-color）
  },
  strokeWidth: {
    type: [Number, String],
    default: 4,
  },
});

const iconComponent = computed(() => {
  // 将 iconName 转换为对应的组件名称
  const componentName = props.name
    .replace(/-([a-z])/g, (match) => match[1].toUpperCase())
    .replace(/^[a-z]/, (match) => match.toUpperCase());
  // console.log(componentName)
  return IconPark[componentName];
});

const iconProps = computed(() => ({
  size: props.size,
  strokeWidth: props.strokeWidth,
  theme: props.theme,
  fill: props.color,
}));
</script>
