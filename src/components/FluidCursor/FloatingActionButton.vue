<template>
  <div ref="dragContainer"
       class="floating-widget"
       :class="{ 'is-dragging': isDragging, 'is-active': isActive }"
       :style="{ top: positionY + 'px' }"
       @mousedown="startDrag"
       @touchstart.passive="startTouchDrag"
       @click="handleClick">
    <div class="widget-content">
      <!-- 图标部分 -->
      <div class="icon-wrapper">
        <IconPark class="main-icon" v-if="enterType === 'qgzx'" name="add-one" size="24"/>
        <IconPark class="main-icon" v-else-if="enterType === 'evaluate'" name="thinking-problem" size="24"/>
      </div>
      <!-- 文字部分 -->
      <div class="text-container" v-if="enterType === 'qgzx'">
        <span class="action-text">岗位申报</span>
      </div>
      <div class="text-container" v-else-if="enterType === 'evaluate'">
        <span class="action-text">操作指引</span>
        <span class="hover-text">点击查看</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import IconPark from '@/components/IconPark/index.vue';

const emit = defineEmits(['doneClick']);
const props = defineProps({
  enterType: {
    type: String,
    default: 'evaluate', // evaluate=综合测评，qgzx=勤工助学
    validator: (value) => ['evaluate', 'qgzx'].includes(value)
  },
});

// 组件状态
const positionY = ref(0);
const isDragging = ref(false);
const isActive = ref(false);
const startDragY = ref(0);
const dragContainer = ref(null);
const isMounted = ref(false);
let maxY = 0;

// 初始化位置
const initPosition = () => {
  if (!isMounted.value || !dragContainer.value) return;

  const containerHeight = dragContainer.value.offsetHeight;
  positionY.value = Math.max(
    20, // 最小顶部边距
    Math.min(
      window.innerHeight / 1.12 - containerHeight / 1.5,
      window.innerHeight - containerHeight - 20 // 底部边距
    )
  );
  updateMaxY();
};

// 拖拽处理逻辑
const startDrag = (e) => {
  e.preventDefault();
  isDragging.value = true;
  startDragY.value = e.clientY - positionY.value;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
};

const startTouchDrag = (e) => {
  isDragging.value = true;
  startDragY.value = e.touches[0].clientY - positionY.value;
  document.addEventListener('touchmove', onTouchDrag);
  document.addEventListener('touchend', stopDrag);
};

const onDrag = (e) => {
  if (!isDragging.value) return;
  updatePosition(e.clientY);
};

const onTouchDrag = (e) => {
  if (!isDragging.value) return;
  updatePosition(e.touches[0].clientY);
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onTouchDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('touchend', stopDrag);
};

// 点击处理
const handleClick = (e) => {
  if (isDragging.value) return;
  isActive.value = true;
  setTimeout(() => isActive.value = false, 300);
  emit('doneClick');
};

// 更新最大Y坐标
const updateMaxY = () => {
  if (dragContainer.value) {
    maxY = window.innerHeight - dragContainer.value.offsetHeight - 20;
  }
};

// 更新位置（带节流）
let lastUpdate = 0;
const updatePosition = (clientY) => {
  const now = Date.now();
  if (now - lastUpdate < 16) return;
  lastUpdate = now;

  let newY = clientY - startDragY.value;
  newY = Math.max(20, Math.min(newY, maxY));

  if (positionY.value !== newY) {
    positionY.value = newY;
  }
};

// 窗口大小处理（带防抖）
let resizeTimer = null;
const handleResize = () => {
  clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    updateMaxY();
    positionY.value = Math.min(positionY.value, maxY);
  }, 100);
};

onMounted(() => {
  isMounted.value = true;
  nextTick(() => {
    initPosition();
    window.addEventListener('resize', handleResize);
  });
});

onUnmounted(() => {
  isMounted.value = false;
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped lang="scss">
.floating-widget {
  position: fixed;
  right: 24px;
  cursor: grab;
  z-index: 9999;
  transition:
    top 0.2s ease-out,
    transform 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28),
    box-shadow 0.2s ease;
  will-change: transform;
  user-select: none;

  &:not(.is-dragging) {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-6px); }
  }
}

.widget-content {
  display: flex;
  align-items: center;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.98);
  box-shadow:
    0 2px 12px rgba(var(--el-color-primary-rgb), 0.2),
    0 4px 16px rgba(var(--el-color-primary-rgb), 0.1);
  padding: 10px 16px;
  gap: 8px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;

  .main-icon {
    color: var(--el-color-primary);
    transition: transform 0.2s ease;
  }
}

.text-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  line-height: 1.3;

  .action-text {
    font-weight: 600;
    color: var(--el-text-color-primary);
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .hover-text {
    font-size: 11px;
    color: var(--el-text-color-secondary);
    opacity: 0;
    height: 0;
    transition:
      opacity 0.2s ease,
      height 0.2s ease;
  }
}

/* 交互状态 */
.floating-widget {
  &:hover:not(.is-dragging) {
    transform: translateX(-4px);

    .widget-content {
      box-shadow:
        0 4px 16px rgba(var(--el-color-primary-rgb), 0.3),
        0 6px 20px rgba(var(--el-color-primary-rgb), 0.15);
    }

    .main-icon {
      transform: scale(1.1);
    }

    .hover-text {
      opacity: 1;
      height: 14px;
    }
  }

  &.is-active {
    .widget-content {
      transform: scale(0.95);
      background: rgba(var(--el-color-primary-rgb), 0.1);
    }
  }

  &.is-dragging {
    cursor: grabbing;
    animation: none;

    .widget-content {
      background: rgba(var(--el-color-primary-rgb), 0.08);
      box-shadow:
        0 4px 20px rgba(var(--el-color-primary-rgb), 0.25);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-widget {
    right: 16px;
    bottom: auto !important;

    &:hover {
      transform: none !important;
    }
  }

  .widget-content {
    padding: 8px 12px;
    border-radius: 20px;
  }

  .icon-wrapper {
    width: 24px;
    height: 24px;
  }

  .text-container {
    .action-text {
      font-size: 13px;
    }

    .hover-text {
      font-size: 10px;
    }
  }
}

@media (max-width: 480px) {
  .floating-widget {
    right: 12px;
  }

  .widget-content {
    padding: 6px 10px;
    gap: 6px;
  }

  .text-container {
    .action-text {
      font-size: 12px;
    }
  }
}
</style>
