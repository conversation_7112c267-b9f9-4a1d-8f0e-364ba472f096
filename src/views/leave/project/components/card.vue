<template>
  <ele-card
    shadow="hover"
    :style="{ margin: '0!important' }"
    :body-style="{
      padding: '0 5px!important',
      marginTop: '8px',
      cursor: 'pointer',
      overflow: 'hidden'
    }"
  >
    <div class="list-item-body" @click.stop="$emit('detail', data)">
      <div style="flex: 1">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <ele-text type="primary" size="md">
            <IconPark name="app-store" size="18" strokeWidth="3" />
            {{ data?.displayName }}
          </ele-text>
          <IconPark
            @click.stop="$emit('delete', data)"
            name="delete"
            size="18"
            strokeWidth="3"
          />
          <!-- <el-tag size="small" effect="light"> {{ jobTypeName }}bbb </el-tag> -->
        </div>

        <div class="job-details">
          <div class="detail-row">
            <IconPark name="calendar" size="16" strokeWidth="3" />
            <span class="span-wrap">
              {{ data?.openFrom }} ~ {{ data?.openTo }}</span
            >
          </div>
          <div class="time-grid">
            <div class="time-item detail-row">
              <IconPark name="plan" size="16" strokeWidth="3" />
              <span class="span-wrap">
                最多请假 {{ data?.maxLeaveDays }} 天</span
              >
            </div>
          </div>
          <div class="detail-row">
            <IconPark name="adjacent-item" size="16" strokeWidth="3" />
            <span class="span-wrap">
              {{ data?.conditionEnabled == '是' ? '已' : '未' }}启用条件</span
            >
          </div>
        </div>
        <div
          style="
            margin-top: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
          "
        >
          <ele-tooltip
            v-for="(t, tIndex) in requestType"
            placement="top"
            effect="light"
            :key="'formButton' + t.id"
            :content="'配置' + t.text + '时需要填写的表单字段'"
          >
            <div class="info-item" @click.stop="$emit('formField', data, t)">
              <span>{{ t.text }}申请表单</span>
            </div>
          </ele-tooltip>
        </div>
        <div
          style="
            margin-top: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
          "
        >
          <ele-tooltip
            v-for="t in requestType"
            placement="top"
            effect="light"
            :key="'formButton' + t.id"
            :content="'配置' + t.text + '的审批流程'"
          >
            <div class="info-item" @click.stop="$emit('process', data, t)">
              <span>{{ t.text }}审批流程</span>
            </div>
          </ele-tooltip>
          <template v-for="t in requestType">
            <ele-tooltip
              v-if="t.id === 'CANCELLATION'"
              placement="top"
              effect="light"
              :key="'formButton' + t.id"
              :content="'配置' + t.text + '的地理范围'"
            >
              <div class="info-item" @click.stop="$emit('address', data, t)">
                <span>{{ t.text }}地理范围</span>
              </div>
            </ele-tooltip>
          </template>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { Timer, Wallet, PriceTag, User } from '@element-plus/icons-vue';
  import { getJobTypeName } from '@/views/qgzx/utils/index.js';
  import IconPark from '@/components/IconPark/index.vue';
  import { ref, watchEffect } from 'vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { template } from 'lodash';

  const props = defineProps({
    data: {
      type: Object,
      required: true
    },
    requestType: {
      type: Array,
      default: () => []
    }
  });

  defineEmits(['edit', 'detail', 'delete', 'formField', 'process', 'address']);
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  // 创建一个响应式变量来存储岗位类型名称
  const jobTypeName = ref('');

  // 监听 jobTypeId 变化
  watchEffect(async () => {
    if (props.data?.jobTypeId) {
      jobTypeName.value = await getJobTypeName(props.data.jobTypeId);
    }
  });
  // 这里可以添加组件特定的方法
</script>
<style scoped>
  @import '../css/type-detail.css';

  .list-item-body {
    padding: 10px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 13px;
    color: #606266;
  }

  .info-item .el-icon {
    font-size: 14px;
  }
</style>
