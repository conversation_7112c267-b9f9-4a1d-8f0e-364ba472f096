<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <el-button size="small" plain class="ele-btn-icon" @click="openEdit()">
          新建
        </el-button>
        <el-button
          v-if="displayMode === 'table'"
          size="small"
          plain
          class="ele-btn-icon"
          @click="remove()"
        >
          删除
        </el-button>
      </template>
      <template #extra>
        <div class="view-switch">
          <el-radio-group v-model="displayMode" size="small">
            <el-radio-button label="fluid">
              <el-icon>
                <Menu />
              </el-icon>
              卡片视图
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon>
                <Grid />
              </el-icon>
              表格视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <!-- 表格 -->
      <ele-pro-table
        v-if="displayMode === 'table'"
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #fieldZh="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.fieldZh }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
      <!-- 流体卡片模式 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
          <el-row v-else :gutter="10">
            <el-col
              v-for="item in fluidData"
              :key="item.id"
              :lg="6"
              :md="8"
              :sm="8"
              :xs="12"
            >
              <detail-card
                :data="item"
                :requestType="requestType"
                @edit="openEdit"
                @detail="openEdit"
                @delete="remove"
                @formField="openFormField"
                @process="openProcess"
                @address="openAddress"
              />
            </el-col>
          </el-row>
          <div v-if="fluidData.length > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, computed, watch } from 'vue';
  import { getLeaveBasicFieldPage, removes } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import {
    ElMessageBox,
    ElMessage as EleMessage,
    ElLoading,
    ElMessage
  } from 'element-plus';
  import Edit from './components/edit.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { comColumns } from './utils/index.js';
  import _ from 'lodash';
  import DetailCard from './components/card.vue';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  /** 显示模式 */
  const displayMode = ref('fluid');

  /** 卡片数据 */
  const fluidData = ref([]);
  /** 加载状态 */
  const loading = ref(false);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);
  /** 表格实例 */
  const tableRef = ref(null);
  /** 是否显示表单弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);

  const bodyStyle = computed(() => {
    return {
      overflow: 'hidden',
      ...(displayMode.value === 'fluid' ? { background: '#f0f2f5' } : {}),
      ...(displayMode.value === 'fluid'
        ? { padding: '0 0 10px !important' }
        : { padding: '0 10px 10px !important' })
    };
  });

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  /** 表格列配置 */
  const columns = ref([]);
  columns.value = comColumns();

  /** 列表选中数据 */
  const selections = ref([]);
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getLeaveBasicFieldPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    selections.value = [];
    currentPage.value = 1;
    if (displayMode.value === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
    // if (where) {
    //   tableRef['value']?.reload?.({ page: 1, where });
    // } else {
    //   //编辑提交table不全局刷新
    //   tableRef['value']?.reload?.();
    // }
  };

  // 获取数据（防抖）
  const fetchData = _.debounce(async () => {
    try {
      loading.value = true;
      const res = await getLeaveBasicFieldPage({
        page: currentPage.value,
        limit: pageSize.value
      });
      fluidData.value = res.list || [];
      total.value = res.count || 0;
    } catch (e) {
      ElMessage.error(e.message);
    } finally {
      loading.value = false;
    }
  }, 300);
  // 监听变化
  watch(
    () => displayMode.value,
    (newMode) => {
      currentPage.value = 1;
      console.log('newMode :>> ', newMode);
      if (newMode)
        newMode === 'fluid' ? fetchData() : tableRef.value?.reload?.();
    },
    { immediate: true }
  );

  // 分页变化
  const handlePageChange = () => {
    if (displayMode.value === 'fluid') {
      fetchData();
    }
  };

  // 每页条数变化
  const handleSizeChange = (size) => {
    pageSize.value = size;
    if (displayMode.value === 'fluid') {
      fetchData();
    }
  };
</script>

<script>
  export default {
    name: 'LEAVEPROJECTFORMFIELDINDEX'
  };
</script>
<style scoped>
  .loading-container {
    padding: 20px;
  }

  .empty-container {
    padding: 40px 0;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }

  .list-item-body {
    display: flex;
    padding: 10px;
  }

  @media screen and (max-width: 576px) {
    .list-item-body {
      display: block;
    }
  }
</style>
