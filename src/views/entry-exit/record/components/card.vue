<template>
  <!-- @click.stop="$emit('detail', data)" -->
  <div class="tech-card">
    <!-- 顶部信息栏 -->
    <div class="card-header">
      <div class="user-info">
        <template v-if="data.user">
          <el-avatar
            v-if="data?.user?.xgh"
            :size="60"
            class="user-avatar"
            :src="
              data?.user?.xgh
                ? `/api/personInfo/${data?.user?.xgh}/photo?access_token=${accessToken}`
                : ''
            "
          >
            {{ data.user?.xm?.charAt(0) || '' }}
          </el-avatar>
          <template v-else>
            <el-avatar
              v-if="data?.user?.xb === '男'"
              :size="60"
              src="/male.png"
              :alt="data?.user?.xb"
              class="user-avatar"
            />
            <el-avatar
              v-else-if="data?.user?.xb === '女'"
              :size="60"
              src="/female.png"
              :alt="data?.user?.xb"
              class="user-avatar"
            />
          </template>
        </template>

        <div class="user-details">
          <div class="student-name-row">
            <h3 class="student-name">
              <span class="highlight">{{ data?.user?.xm || '--' }}</span>
            </h3>
            <div class="student-meta">
              <span>{{ data?.user?.xgh || '--' }}</span>
            </div>
          </div>
          <div class="meta-info">
            <span class="apply-time detail-row">
              <IconPark name="history" size="15" strokeWidth="3" />
              <span class="span-wrap">{{ data?.dateTime }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 岗位信息卡片 -->
    <div class="job-card">
      <!-- <div class="job-title detail-row">
        <IconPark name="app-store" size="18" strokeWidth="3" />
        <span class="span-wrap">{{
          data.jobApplication?.jobName || '未知岗位'
        }}</span>
      </div> -->
      <div class="job-details">
        <!-- 学院 -->
        <div class="detail-row" v-if="data?.user?.xyName">
          <IconPark name="read-book" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.user?.xyName || '未知' }}</span>
        </div>
        <!-- 专业 -->
        <div class="detail-row" v-if="data?.user?.zyName">
          <IconPark name="connection-box" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.user?.zyName || '未知' }}</span>
        </div>
        <!-- 班级 -->
        <div class="detail-row" v-if="data?.user?.bjName">
          <IconPark name="classroom" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.user?.bjName || '未知' }}</span>
        </div>
        <div class="detail-row">
          <IconPark name="local" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.campus || '未知' }}</span>
        </div>
        <div class="detail-row">
          <IconPark name="gate-machine" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.source || '未知' }}</span>
        </div>

        <!-- <div class="detail-row">
          <IconPark name="time" size="16" strokeWidth="3" />
          <span class="span-wrap">{{ data?.dateTime }}</span>
        </div> -->
        <!-- <div class="time-grid">
          <div class="time-item detail-row">
            <IconPark name="plan" size="16" strokeWidth="3" />
            <span class="span-wrap">{{ formatWorkDays(data.jobApplication?.workDays) }}</span>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Timer, Wallet, PriceTag, User } from '@element-plus/icons-vue';
  import { getJobTypeName } from '@/views/qgzx/utils/index.js';
  import IconPark from '@/components/IconPark/index.vue';
  import { ref, watchEffect } from 'vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { getToken } from '@/utils/token-util.js';

  const props = defineProps({
    data: {
      type: Object,
      required: true
    },
    requestType: {
      type: Array,
      default: () => []
    }
  });

  defineEmits(['edit', 'detail', 'delete', 'entry-exit-type', 'abnormal-time']);
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  const accessToken = getToken();

  // 创建一个响应式变量来存储岗位类型名称
  const jobTypeName = ref('');
  // 监听 jobTypeId 变化
  watchEffect(async () => {
    if (props.data?.jobTypeId) {
      jobTypeName.value = await getJobTypeName(props.data.jobTypeId);
    }
  });
  // 这里可以添加组件特定的方法
</script>
<style scoped>
  @import '../css/type-detail.css';

  .list-item-body {
    padding: 10px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 13px;
    color: #606266;
  }

  .info-item .el-icon {
    font-size: 14px;
  }
</style>
