export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'user.xgh',
      label: '学号',
      type: 'input',
      minWidth: 100,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true
    },
    {
      prop: 'user.xm',
      slot: 'xm',
      label: '姓名',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    // {
    //   prop: 'user.xb',
    //   label: '性别',
    //   type: 'input',
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true
    // },
    {
      prop: 'user.xyName',
      label: '学院',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true
    },
    {
      prop: 'user.zyName',
      label: '专业',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'user.bjName',
      label: '班级',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'user.njName',
      label: '年级',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'campus',
      label: '校区',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'source',
      label: '来源',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'dateTime',
      label: '出入时间',
      minWidth: 90,
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    }
  ];
}
