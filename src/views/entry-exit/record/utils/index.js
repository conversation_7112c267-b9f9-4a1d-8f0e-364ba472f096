export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      label: '学号',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true
    },
    {
      prop: 'xm',
      slot: 'xm',
      label: '姓名',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'xb',
      label: '性别',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'fieldEn',
      label: '学院',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true
    },
    {
      prop: 'alias',
      label: '专业',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'display',
      label: '班级',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'condition',
      label: '年级',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'condition',
      label: '所在校区',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'condition',
      label: '闸机名称',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    },
    {
      prop: 'condition',
      label: '出入时间',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true
    }
  ];
}
