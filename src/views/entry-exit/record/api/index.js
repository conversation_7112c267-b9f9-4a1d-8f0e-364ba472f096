import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getEntryExitRecordPage(params) {
  const res = await request.get('/entry-exit/record/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询tab栏
 */
export async function getRecordTabsPage(params) {
  const res = await request.get('/entry-exit/record/tabs', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
