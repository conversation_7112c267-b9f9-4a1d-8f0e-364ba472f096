/* 科技感卡片设计 */
.tech-card {
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  -webkit-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin-bottom: 16px;
  cursor: pointer;
}

.tech-card:hover {
  -webkit-transform: translateY(-4px);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(99, 102, 241, 0.2);
}

/* 卡片头部 */
.card-header {
  padding: 6px 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-left: 10px;
  margin-right: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

/* 修改用户信息区域样式 */
.user-details {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.student-name-row {
  display: flex;
  align-items: center;
  gap: 6px; /* 减小间距 */
  margin-bottom: 4px; /* 添加下边距 */
}

.student-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.student-meta {
  padding-top: 5px;
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
}

.meta-info {
  margin: 4px 0 0; /* 减小上边距 */
  color: #555;
  font-size: 13px;

  .apply-time {
    display: inline-flex; /* 改为行内布局 */
    align-items: center;
  }
}

/* 岗位信息卡片 */
.job-card {
  padding: 10px 16px;
  background: white;
}

.job-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: #34495e;
}

.job-details {
  margin-top: 12px;
}

.detail-row {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.detail-row .span-wrap {
  padding-left: 10px;
  margin-right: 8px;
  color: #555;
  font-size: 14px;
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}

.time-grid {
  margin-top: 8px;
}

.time-item {
  display: -webkit-box; /* 兼容旧版 QQ 浏览器 */
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 6px 8px 6px 0;
  -webkit-border-radius: 6px;
  border-radius: 6px;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-grid {
    grid-template-columns: 1fr;
  }

}
