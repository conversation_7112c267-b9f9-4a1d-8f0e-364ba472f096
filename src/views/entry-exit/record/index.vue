<template>
  <ele-page hide-footer flex-table>
    <search @search="reload" />
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <ele-tabs
          type="tag"
          size="small"
          v-model="activeName"
          :items="tabsItems"
        >
          <template #label="{ item, label }">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <div class="toolbar-container">
          <div class="view-switch">
            <el-radio-group v-model="displayMode" size="small">
              <el-radio-button value="fluid">
                <el-icon>
                  <Menu />
                </el-icon>
                卡片视图
              </el-radio-button>
              <el-radio-button value="table">
                <el-icon>
                  <Grid />
                </el-icon>
                表格视图
              </el-radio-button>
            </el-radio-group>
          </div>
          <el-button
            size="small"
            type="primary"
            @click="exportData()"
            class="create-btn"
          >
            <el-icon>
              <Download />
            </el-icon>
            导出
          </el-button>
        </div>
      </template>
      <!-- 表格 -->
      <ele-pro-table
        v-if="displayMode === 'table'"
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #fieldZh="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.fieldZh }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
      <!-- 流体卡片模式 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无数据" />
          </div>
          <el-row v-else :gutter="10">
            <el-col
              v-for="item in fluidData"
              :key="item.id"
              :lg="8"
              :md="12"
              :sm="12"
              :xs="24"
            >
              <detail-card
                :data="item"
                :requestType="requestType"
                @edit="openEdit"
                @detail="openEdit"
                @delete="remove"
                @entry-exit-type="openEntryExitType"
                @abnormal-time="openAbnormalTime"
              />
            </el-col>
          </el-row>
          <div v-if="fluidData.length > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch, computed } from 'vue';
  import { getEntryExitRecordPage, getRecordTabsPage } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import Edit from './components/edit.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { comColumns } from './utils/index.js';
  import Search from './components/search.vue';
  import DetailCard from './components/card.vue';
  import { ElMessage } from 'element-plus';
  import _ from 'lodash';
  import { formMateSearchDataToStr } from '@/utils/common.js';
  import { getToken } from '@/utils/token-util';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const userStore = useUserStore();
  const accessToken = getToken();
  const { pageHeight } = storeToRefs(userStore);
  const BASE_URL = import.meta.env.BASE_URL;
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);
  let pathArray = path.split('/');
  let routeProjectId = pathArray[3];
  console.log('pathArray :>> ', pathArray);

  /** 标签页相关 */
  const activeName = ref(null);
  const tabsItems = ref([]);

  /** 显示模式 */
  const displayMode = ref('fluid'); // 'table' 或 'fluid'

  /** 数据相关 */
  const fluidData = ref([]);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);

  /** 表格实例 */
  const tableRef = ref(null);
  /** 加载状态 */
  const loading = ref(false);

  /** 是否显示表单弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };
  const bodyStyle = computed(() => {
    return {
      overflow: 'hidden',
      ...(displayMode.value === 'fluid' ? { background: '#f0f2f5' } : {}),
      ...(displayMode.value === 'fluid'
        ? { padding: '0 0 10px !important' }
        : { padding: '0 10px 10px !important' })
    };
  });
  /** 表格列配置 */
  const columns = ref([]);
  columns.value = comColumns();
  /** 列表选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getEntryExitRecordPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      projectId: routeProjectId,
      tabCode: activeName.value
    });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    displayMode.value === 'fluid' ? fetchData() : tableRef.value?.reload?.();
    // if (where) {
    //   tableRef['value']?.reload?.({ page: 1, where });
    // } else {
    //   //编辑提交table不全局刷新
    //   tableRef['value']?.reload?.();
    // }
  };
  /**查询tab栏 */
  const getTabItems = async () => {
    const res = await getRecordTabsPage({ projectId: routeProjectId });
    console.log('res :>> ', res);
    let tabItems = [];
    res.forEach((item) => {
      tabItems.push({
        label: item.name,
        name: item.code
      });
    });
    tabsItems.value = tabItems;
    activeName.value =
      tabsItems.value.length > 0 ? tabsItems.value[0].name : null;
  };
  getTabItems();

  // 计算属性 - 获取当前查询参数
  const queryParams = computed(() => ({
    page: currentPage.value,
    limit: pageSize.value,
    tabCode: activeName.value,
    projectId: routeProjectId
  }));

  /** 表格搜索参数 */
  const lastWhere = ref({});
  // 修改fetchData函数以考虑搜索条件
  const fetchData = _.debounce(async () => {
    if (!activeName.value) return;
    try {
      loading.value = true;
      const params = {
        ...queryParams.value
        // ...lastWhere.value
      };
      const res = await getEntryExitRecordPage(params);
      fluidData.value = res.list || [];
      total.value = res.count || 0;
    } catch (e) {
      ElMessage.error(e.message);
    } finally {
      loading.value = false;
    }
  }, 300);

  /**监听activeName变化 */
  watch(
    activeName,
    (newVal) => {
      console.log('activeName newVal:>> ', newVal);
      if (newVal) {
        currentPage.value = 1;
        if (displayMode.value === 'fluid') {
          fetchData();
        } else {
          tableRef.value?.reload?.();
        }
      }
    },
    { immediate: true }
  );
  /** 根据检索条件导出数据 */
  const exportData = () => {
    let where = { ...queryParams.value };
    let searchStr = formMateSearchDataToStr(where);
    loading.value = true;
    console.log('searchStr :>> ', searchStr);
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/entry-exit/record/exportData?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };
</script>

<script>
  export default {
    name: 'ENTRYEXITRECORDINDEX'
  };
</script>
<style scoped>
  .toolbar-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .view-switch {
    margin-right: 12px;
  }

  .create-btn {
    margin-left: auto;
  }

  @media screen and (max-width: 768px) {
    .toolbar-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .view-switch {
      margin-right: 0;
      margin-bottom: 8px;
    }

    .create-btn {
      margin-left: 0;
      width: 100%;
    }
  }
  .loading-container {
    padding: 20px;
  }

  .empty-container {
    padding: 40px 0;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }

  .list-item-body {
    display: flex;
    padding: 10px;
  }

  @media screen and (max-width: 576px) {
    .list-item-body {
      display: block;
    }
  }
</style>
