import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getEventTypePage(params) {
  const res = await request.get('/entry-exit/project/event-type/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 新增/修改出入类型
 */
export async function operation(data) {
  const res = await request.post(
    '/entry-exit/project/event-type/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/entry-exit/project/event-type/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
