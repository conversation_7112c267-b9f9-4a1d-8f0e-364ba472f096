<template>
  <ele-drawer
    size="40%"
    :title="currentProjectName + '出入类型管理'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-page hide-footer flex-table>
      <ele-card
        flex-table
        :body-style="{
          padding: '0 8px 10px 8px!important',
          overflow: 'hidden'
        }"
      >
        <!-- 表格 -->
        <ele-pro-table
          ref="tableRef"
          row-key="id"
          :columns="columns"
          :datasource="datasource"
          :border="true"
          :show-overflow-tooltip="true"
          v-model:selections="selections"
          tooltip-effect="light"
          highlight-current-row
          :footer-style="{ paddingBottom: '3px' }"
          style="padding-bottom: 0"
        >
          <template #toolbar>
            <el-button
              size="small"
              plain
              class="ele-btn-icon"
              @click="openEdit()"
            >
              新建
            </el-button>
            <el-button
              size="small"
              plain
              class="ele-btn-icon"
              @click="remove()"
            >
              删除
            </el-button>
          </template>
          <template #name="{ row }">
            <ele-tooltip content="编辑" placement="left" effect="light">
              <el-link type="primary" underline="never" @click="openEdit(row)">
                {{ row.name }}
              </el-link>
            </ele-tooltip>
          </template>
        </ele-pro-table>
      </ele-card>
      <edit
        v-model="showEdit"
        :data="editData"
        :currentProjectId="currentProjectId"
        @done="reload"
      />
    </ele-page>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { getEventTypePage, removes } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import {
    ElMessageBox,
    ElMessage as EleMessage,
    ElLoading
  } from 'element-plus';
  import Edit from './components/edit.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { comColumns } from './utils/index.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 项目ID*/
    currentProjectId: String,
    /** 项目名称*/
    currentProjectName: String
  });

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 是否显示表单弹窗 */
  const showEdit = ref(false);
  /** 表格列配置 */
  const columns = ref([]);
  columns.value = comColumns();

  /** 列表选中数据 */
  const selections = ref([]);

  /** 编辑回显数据 */
  const editData = ref(null);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getEventTypePage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      projectId: props.currentProjectId
    });
    return res;
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        reload();
      }
    }
  );
</script>

<script>
  export default {
    name: 'ENTRYEXITTYPEINDEX'
  };
</script>
