/* 基础重置 */
.modern-job-detail * {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

.modern-job-detail {
  position: relative;
  margin: 0 auto;
  padding: 6.4px;
  padding: 0.4rem;
  /*background-color: #f9fbfd;*/
  /*min-height: 100vh;*/
  overflow-x: hidden;
}

.modern-card-body {
  padding: 0.8rem;
}

.modern-content {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 内容区域 */
.modern-job-detail .modern-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 9.6px;
  gap: 0.6rem;
}

.status-badge {
  margin-left: 12px;
  display: inline-block;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  font-size: 0.5rem;
  font-weight: 500;
  white-space: nowrap;
  color: #fff;
  text-align: center;
  transition: all 0.3s ease;
}

/* 待处理：主色变量 + 渐变 + fallback */
.status-badge.pending {
  background-color: #409EFF;
  background-image: linear-gradient(135deg, var(--el-color-primary, #409EFF), #a3d4ff);
}

/* 已通过：绿色渐变 */
.status-badge.approved {
  background-color: #67c23a; /* fallback */
  background-image: linear-gradient(135deg, #4caf50, #7fda82);

}

/* 已驳回：红色渐变 */
.status-badge.rejected {
  background-color: #f56c6c; /* fallback */
  background-image: linear-gradient(135deg, #f56c6c, #f89b9b);
}

/* 已完成：灰色渐变 */
.status-badge.completed {
  background-color: #909399; /* fallback */
  background-image: linear-gradient(135deg, #909399, #c0c4cc);
}

/* 卡片样式 */
.modern-job-detail .modern-card {
  background-color: white;
  border-radius: 10px;
  border-radius: 0.625rem;
  -webkit-box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  -webkit-transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transition: transform 0.3s ease, box-shadow 0.3s ease, -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.modern-job-detail .modern-card:hover {
  -webkit-transform: translateY(-3px);
  transform: translateY(-3px);
  -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 卡片头部 */
.modern-job-detail .modern-card-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6.4px;
  padding: 0.4rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: rgba(64, 158, 255, 0.03);
  background-color: rgba(var(--el-color-primary-rgb), 0.03);
}

.modern-job-detail .modern-card-header h3 {
  margin: 0;
  font-size: 10.56px;
  font-size: 0.66rem;
  color: #2c3e50;
  font-weight: 600;
}

/* 卡片图标 */
.modern-job-detail .modern-card-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background-color: rgba(64, 158, 255, 0.1);
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  color: #409eff;
  color: var(--el-color-primary);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-right: 8px;
  margin-right: 0.5rem;
}

/* 网格布局 */
.modern-job-detail .modern-info-grid {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: (minmax(220px, 1fr)) [ auto-fill ];
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 0.6rem;
}


/* 标签容器 */
.modern-job-detail .modern-tag-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  grid-gap: 9.6px;
  gap: 9.6px;
  gap: 0.6rem;
}

.modern-tag {
  display: flex;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.08); /* fallback */
  color: var(--el-color-primary);
  padding: 0.4rem;
  border-radius: 16px;
  font-size: 0.6rem;
  transition: all 0.3s ease;
}

.modern-tag .el-icon {
  font-size: 0.6rem;
  margin-right: 0.2rem;
}

.modern-tag:hover {
  background-color: rgba(64, 158, 255, 0.15); /* fallback */
  transform: translateY(-2px);
}

/* 装饰元素 */
.modern-job-detail .modern-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.modern-job-detail .modern-dot {
  position: absolute;
  border-radius: 50%;
  -webkit-filter: blur(30px);
  filter: blur(30px);
  opacity: 0.1;
}

.modern-info-value {
  font-size: 0.6rem;
  color: #2c3e50;
  font-weight: 500;
}

.modern-feature-list {
  padding: 0.1rem;
  list-style: none;
  font-size: 0.6rem;
  display: flex;
  flex-direction: column; /* 👈 垂直排列子元素 */
  align-items: flex-start;
  gap: 0.4rem;
}

.modern-feature-list .modern-feature-text {
  color: #34495e;
  font-size: 0.6rem;
  line-height: 1.4;
}

.modern-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding: 1rem 0;
}

.modern-apply-btn {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(64, 158, 255, 0.3); /* fallback */
  box-shadow: 0 3px 10px rgba(var(--el-color-primary-rgb), 0.3); /* main */

  .modern-btn-icon {
    margin-left: 0.3rem;
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(64, 158, 255, 0.4); /* fallback */
    box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.4); /* main */

    .modern-btn-icon {
      transform: translateX(3px);
    }
  }
}

/* QQ浏览器特定修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modern-job-detail .modern-card {
    -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  }

  .modern-job-detail .modern-tag {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}


.description-container{
  margin-top: 6px;
}
.ellipsis-text {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.job-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: #34495e;
}

.job-details {
  margin-top: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.detail-row .span-wrap {
  padding-left: 10px;
  margin-right: 8px;
  /*color: #7f8c8d;*/
  color: #555;
  font-size: 14px;
  flex-shrink: 0;
}

.time-grid {
  margin-top: 8px;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 6px 8px 6px 0;
  border-radius: 6px;
}



/* 新增的操作按钮样式 */
.action-buttons {
  display: flex;
  border-top: 1px dashed rgba(0, 0, 0, 0.05);
  padding: 8px 0;
  background-color: #f9fafb;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  margin: 0 4px;
  border-radius: 4px;
  color: #5a5e66;
  background: transparent;
  border: none;
  font-size: 12px;
  transition: all 0.3s;
}

.action-btn:hover {
  background-color: #edf2f7;
  color: #409eff;
}

.action-btn span {
  margin-top: 4px;
}

.action-btn i {
  font-size: 16px;
}
