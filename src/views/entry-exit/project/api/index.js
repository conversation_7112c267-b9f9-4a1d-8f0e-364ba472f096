import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getLeaveProjectPage(params) {
  const res = await request.get('/entry-exit/project/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 可选销假模式
 */
export async function getLeaveProjectOptionalCancellationMode(params) {
  const res = await request.get('/leave/project/optionalCancellationMode', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 可选请假时长计算模式
 */
export async function getLeaveProjectOptionalDurationMode(params) {
  const res = await request.get('/leave/project/optionalDurationMode', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 可选的三态布尔类型（是或否）
 */
export async function getLeaveProjectOptionalTriStateBoolean(params) {
  const res = await request.get('/leave/project/optionalTriStateBoolean', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getLeaveProjectInfo(id) {
  const res = await request.get('/leave/project/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post('/entry-exit/project/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/entry-exit/project/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 可选申请类型
 */
export async function optionalRequestType(data) {
  const res = await request.get('/leave/common/optionalRequestType', data);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
