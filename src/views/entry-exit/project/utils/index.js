export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'name',
      slot: 'name',
      label: '项目名称',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: { span: 24 }
    },
    {
      prop: 'createdAtText',
      label: '创建时间',
      type: 'input',
      minWidth: 140,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: { span: 24 }
    }
    // {
    //   prop: 'displayName',
    //   slot: 'name',
    //   label: '状态',
    //   type: 'input',
    //   minWidth: 110,
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true,
    //   colProps: { span: 12 }
    // }
    // {
    //   prop: 'openFrom',
    //   label: '开放时间',
    //   type: 'datetime',
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true,
    //   colProps: { span: 12 }
    // },
    // {
    //   prop: 'openTo',
    //   label: '结束时间',
    //   type: 'datetime',
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true,
    //   colProps: { span: 12 }
    // },
    // {
    //   prop: 'maxLeaveDays',
    //   label: '最大请假天数',
    //   type: 'inputNumber',
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true,
    //   colProps: { span: 12 }
    // },
    // {
    //   prop: 'durationMode',
    //   label: '请假时长计算模式',
    //   slot: 'durationMode',
    //   type: 'dictSelect',
    //   typeKey: 'select',
    //   props: {
    //     code: 'durationMode',
    //     filterable: true,
    //     dicQueryParams: {
    //       dictFieldUrl: '/leave/project/optionalDurationMode',
    //       valueField: 'id',
    //       textField: 'text'
    //     }
    //   },
    //   showFlag: '否',
    //   selfModifyFlag: '否',
    //   required: true,
    //   colProps: { span: 12 }
    // },
    // {
    //   prop: 'cancellationMode',
    //   label: '销假模式',
    //   slot: 'cancellationMode',
    //   type: 'dictSelect',
    //   typeKey: 'select',
    //   props: {
    //     code: 'cancellationMode',
    //     filterable: true,
    //     dicQueryParams: {
    //       dictFieldUrl: '/leave/project/optionalCancellationMode',
    //       valueField: 'id',
    //       textField: 'text'
    //     }
    //   },
    //   showFlag: '否',
    //   selfModifyFlag: '否',
    //   required: true
    // },
    // {
    //   prop: 'allowExtension',
    //   label: '是否允许续假',
    //   slot: 'allowExtension',
    //   type: 'dictSelect',
    //   typeKey: 'select',
    //   props: {
    //     code: 'allowExtension',
    //     filterable: true,
    //     dicQueryParams: {
    //       dictFieldUrl: '/leave/project/optionalTriStateBoolean',
    //       valueField: 'id',
    //       textField: 'text'
    //     }
    //   },
    //   showFlag: '否',
    //   selfModifyFlag: '否',
    //   required: true,
    //   colProps: { span: 8 }
    // },
    //   {
    //     prop: 'enableNotification',
    //     label: '启用消息通知',
    //     slot: 'enableNotification',
    //     type: 'dictSelect',
    //     typeKey: 'select',
    //     props: {
    //       code: 'enableNotification',
    //       filterable: true,
    //       dicQueryParams: {
    //         dictFieldUrl: '/leave/project/optionalTriStateBoolean',
    //         valueField: 'id',
    //         textField: 'text'
    //       }
    //     },
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'cancellationAfterLeaveEnd',
    //     label: '假期结束后自动销假(无需申请)',
    //     type: 'switch',
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'cancellationApplyInArea',
    //     label: '仅允许在地理范围内申请销假',
    //     type: 'switch',
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'cancellationNoApproval',
    //     label: '无需销假审批',
    //     type: 'switch',
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'cancellationAutoApproveInArea',
    //     label: '在地理范围内销假自动通过',
    //     type: 'switch',
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'conditionEnabled',
    //     label: '条件启用',
    //     type: 'switch',
    //     showFlag: '否',
    //     selfModifyFlag: '否',
    //     required: true,
    //     colProps: { span: 8 }
    //   },
    //   {
    //     prop: 'conditions',
    //     label: '生效条件',
    //     type: 'conditions'
    //   }
  ];
}
