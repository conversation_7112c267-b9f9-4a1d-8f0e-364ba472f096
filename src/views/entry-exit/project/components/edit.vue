<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    size="30%"
    :title="isUpdate ? '修改 [' + data.name + ']' : '添加'"
    :append-to-body="true"
    :destroy-on-close="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="formItems"
      :grid="{ span: 24 }"
      labelWidth="auto"
      label-position="top"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <ele-text
        style="float: left"
        v-if="validMsg"
        type="danger"
        :icon="CloseCircleOutlined"
      >
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="handleSubmit"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operation } from '../api/index.js';
  import { comColumns } from '../utils/index.js';
  import ProForm from '@/components/ProForm/index.vue';
  import { generateForm, generateRandomString } from '@/utils/common_bak2.js';
  import { CloseCircleOutlined } from '@/components/icons/index.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 试卷ID*/
    paperId: String
  });

  let RandomString = ref('');
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0
  });

  const formItems = ref([]);
  const formData = ref(null);

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  const validMsgCount = ref(0);
  /** 存放子组件的数组 */
  let resultArr = reactive([]);

  const handleSubmit = async () => {
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []; //每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save();
      } else {
        validMsg.value = `选项设置共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  };

  /** 保存编辑 */
  const save = () => {
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let data = { ...form, conditions: formData.value };
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done', isUpdate.value, data);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        const formInitData = generateForm(formItems.value);
        formItems.value =
          comColumns().filter((i) => i.prop !== 'createdAtText') || [];
        if (props.data) {
          assignFields({ ...formInitData, ...props.data });
          RandomString.value = generateRandomString(10);
          isUpdate.value = true;
        } else {
          resetFields(formInitData); // 一次性reset新的字段
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
