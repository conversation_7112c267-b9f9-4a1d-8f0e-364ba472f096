<template>
  <ele-card
    shadow="hover"
    :style="{ margin: '0!important' }"
    :body-style="{
      padding: '0 5px!important',
      marginTop: '8px',
      cursor: 'pointer',
      overflow: 'hidden'
    }"
  >
    <div class="list-item-body" @click.stop="$emit('detail', data)">
      <div style="flex: 1">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <ele-text type="primary" size="md">
            <IconPark name="app-store" size="18" strokeWidth="3" />
            {{ data?.name }}
          </ele-text>
          <IconPark
            @click.stop="$emit('delete', data)"
            name="delete"
            size="18"
            strokeWidth="3"
          />
          <!-- <el-tag size="small" effect="light"> {{ jobTypeName }}bbb </el-tag> -->
        </div>

        <div class="job-details">
          <div class="time-grid">
            <div class="time-item detail-row">
              <IconPark name="plan" size="16" strokeWidth="3" />
              <span class="span-wrap"> {{ data?.createdAtText }} 创建 </span>
            </div>
          </div>
        </div>
        <div
          style="
            margin-top: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
          "
        >
          <ele-tooltip
            placement="top"
            effect="light"
            :content="'配置出入项目的出入类型'"
          >
            <div class="info-item" @click.stop="$emit('entry-exit-type', data)">
              <span>出入类型</span>
            </div>
          </ele-tooltip>
          <ele-tooltip
            placement="top"
            effect="light"
            :content="'配置出入项目的异常时间段规则'"
          >
            <div class="info-item" @click.stop="$emit('abnormal-time', data)">
              <span>异常时间段规则</span>
            </div>
          </ele-tooltip>
          <ele-tooltip
            placement="top"
            effect="light"
            :content="'查询出入项目的出入记录'"
          >
            <div class="info-item" @click.stop="$emit('record', data)">
              <span>出入记录</span>
            </div>
          </ele-tooltip>
          <!-- <ele-tooltip
            v-for="(t, tIndex) in requestType"
            placement="top"
            effect="light"
            :key="'formButton' + t.id"
            :content="'配置' + t.text + '时需要填写的表单字段'"
          >
            <div class="info-item" @click.stop="$emit('entry-exit-type', data, t)">
              <span>{{ t.text }}申请表单</span>
            </div>
          </ele-tooltip> -->
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
  import { Timer, Wallet, PriceTag, User } from '@element-plus/icons-vue';
  import { getJobTypeName } from '@/views/qgzx/utils/index.js';
  import IconPark from '@/components/IconPark/index.vue';
  import { ref, watchEffect } from 'vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { template } from 'lodash';

  const props = defineProps({
    data: {
      type: Object,
      required: true
    },
    requestType: {
      type: Array,
      default: () => []
    }
  });

  defineEmits([
    'edit',
    'detail',
    'delete',
    'entry-exit-type',
    'abnormal-time',
    'record'
  ]);
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  // 创建一个响应式变量来存储岗位类型名称
  const jobTypeName = ref('');

  // 监听 jobTypeId 变化
  watchEffect(async () => {
    if (props.data?.jobTypeId) {
      jobTypeName.value = await getJobTypeName(props.data.jobTypeId);
    }
  });
  // 这里可以添加组件特定的方法
</script>
<style scoped>
  @import '../css/type-detail.css';

  .list-item-body {
    padding: 10px;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 13px;
    color: #606266;
  }

  .info-item .el-icon {
    font-size: 14px;
  }
</style>
