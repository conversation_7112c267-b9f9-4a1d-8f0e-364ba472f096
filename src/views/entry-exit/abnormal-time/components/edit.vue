<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="500"
    :title="isUpdate ? '修改 [' + form.name + ']' : '添加'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="formItems"
      :grid="{ span: 24 }"
      labelWidth="auto"
      label-position="top"
      @updateValue="setFieldValue"
    >
      <template #typeText="{ item, model, updateValue }">
        <dict-data
          placeholder="请选择字段类型"
          code="consultantList"
          type="select"
          :refresh="RandomString"
          :dicQueryParams="{
            dictFieldUrl: 'leave/project/form_field/optionalType',
            valueField: 'id',
            textField: 'text',
            valueType: 'Object'
          }"
          :model-value="form[item.prop]"
          @update:modelValue="(value) => updateFieldEnValue(item.prop, value)"
        />
      </template>
      <template #codeTypeText="{ item, model, updateValue }">
        <dict-data
          placeholder="请选择加载数据类型"
          code="consultantList"
          type="select"
          :refresh="RandomString"
          :dicQueryParams="{
            dictFieldUrl: 'leave/project/form_field/optionalCodeType',
            valueField: 'id',
            textField: 'text',
            valueType: 'Object'
          }"
          :model-value="form[item.prop]"
          @update:modelValue="(value) => updateFieldEnValue(item.prop, value)"
        />
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operation } from '../api/index.js';
  import { comColumns } from '../utils/index.js';
  import ProForm from '@/components/ProForm/index.vue';
  import { generateForm, generateRandomString } from '@/utils/common_bak2.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 请假类型ID*/
    currentProjectId: String,
    /** 申请类型 */
    requestTypeId: String
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0
  });

  const formItems = ref([]);

  /** 保存编辑 */
  const save = () => {
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let data = {
        ...form,
        projectId: props.currentProjectId,
        requestType: props.requestTypeId
      };
      if (data.options) {
        data.options = data.options.split('#');
      }
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done', isUpdate.value, data);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };
  /** 更新值 */
  const updateFieldEnValue = (prop, value) => {
    if (value) {
      if (prop == 'typeText') {
        form['typeText'] = value.text;
        form.type = {
          id: value.id,
          text: value.text
        };
        let baseColumns = comColumns() || [];
        if (value.id == 'RADIO' || value.id == 'CHECKBOX') {
          if (form.options) {
            formItems.value = baseColumns.filter(
              (i) => i.prop != 'codeTypeText'
            );
          }
          if (form.codeTypeText) {
            formItems.value = baseColumns.filter((i) => i.prop != 'options');
          }
          if (!form.options && !form.codeTypeText) {
            formItems.value = baseColumns;
          }
        } else {
          formItems.value = baseColumns.filter(
            (item) => item.prop && !item.disabled
          );
          form.options = null;
          form.codeType = null;
          form.codeTypeText = null;
        }
      } else if (prop == 'codeTypeText') {
        form['codeTypeText'] = value.text;
        form.codeType = {
          id: value.id,
          text: value.text
        };
      }
    } else {
      if (prop == 'typeText') {
        form['typeText'] = '';
        form.type = null;
      } else if (prop == 'codeTypeText') {
        form['codeTypeText'] = '';
        form.codeType = null;
      }
    }
  };
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  let RandomString = ref('');
  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        RandomString.value = generateRandomString(10);
        let baseColumns = comColumns() || [];
        formItems.value = baseColumns.filter(
          (item) => item.prop && !item.disabled
        );
        const formInitData = generateForm(formItems.value);
        formInitData.projectId = props.currentProjectId;
        formInitData.projectId = props.requestTypeId;
        if (props.data) {
          // assignFields(props.data);
          assignFields({
            ...formInitData,
            ...props.data
            // projectId: props.currentProjectId
          });
          isUpdate.value = true;
          if (props.data.options) {
            formItems.value = baseColumns.filter(
              (i) => i.prop != 'codeTypeText'
            );
          }
          if (props.data.codeTypeText) {
            formItems.value = baseColumns.filter((i) => i.prop != 'options');
          }
        } else {
          resetFields(formInitData); // 一次性reset新的字段
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
  watch(
    () => [form.options, form.codeType],
    ([options, codeType]) => {
      let baseColumns = comColumns() || [];
      if (options && !codeType) {
        formItems.value = baseColumns.filter((i) => i.prop != 'codeTypeText');
      } else if (!options && codeType) {
        formItems.value = baseColumns.filter((i) => i.prop != 'options');
      } else if (!options && !codeType) {
        if (
          form.type &&
          (form.type.id == 'RADIO' || form.type.id == 'CHECKBOX')
        ) {
          formItems.value = baseColumns;
        } else {
          formItems.value = baseColumns.filter(
            (item) => item.prop && !item.disabled
          );
        }
      }
    },
    { deep: true } // 开启深度监听
  );
</script>
