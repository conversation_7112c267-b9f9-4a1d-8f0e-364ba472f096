import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getAbnormalTimePage(params) {
  const res = await request.get(
    '/entry-exit/project/time-window-anomaly-rule/page',
    {
      params
    }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 新增/修改出入类型
 */
export async function operation(data) {
  const res = await request.post(
    '/entry-exit/project/time-window-anomaly-rule/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post(
    '/entry-exit/project/time-window-anomaly-rule/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
