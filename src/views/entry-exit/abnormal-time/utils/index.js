export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'name',
      slot: 'name',
      label: '名称',
      type: 'input',
      minWidth: 80,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'enabled',
      label: '启用',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'startTimeOfDay',
      label: '开始时间',
      type: 'time',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'endTimeOfDay',
      label: '结束时间',
      type: 'time',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'severity',
      label: '严重程度',
      slot: 'severity',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'severity',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl:
            '/entry-exit/project/time-window-anomaly-rule/optionalSeverity',
          valueField: 'id',
          textField: 'text'
        }
      },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: { span: 24 }
    }
  ];
}
