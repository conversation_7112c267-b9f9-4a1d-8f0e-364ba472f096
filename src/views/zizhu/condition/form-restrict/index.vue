<template>
  <ele-drawer size="60%"
              :title="'['+(currentProject&&currentProject.xmmc)+'] 申请限制条件设置11'"
              style="max-width: 100%"
              :destroy-on-close="true"
              :append-to-body="true"
              :close-on-click-modal="false"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <!--        datasource== {{datasource}}<br/><br/>-->
    <!--        columns== {{columns}}<br/>-->
    <!--    formTestrict== {{ formTestrict }}<br/>-->


    <!--    <el-form ref="formRef" :model="datasource" label-width="0px" @submit.prevent="" size="small">-->
    <!--      <div style="overflow: auto">-->
    <!--        <ele-table style="min-width: 580px; table-layout: fixed;text-align: left" border>-->
    <!--          <thead>-->
    <!--          <tr>-->
    <!--            <template v-for="header in columns">-->
    <!--              <th :style="{ 'width': header.width+'px'}">{{ header.label }}</th>-->
    <!--            </template>-->
    <!--          </tr>-->
    <!--          </thead>-->
    <!--          <tbody>-->
    <!--          <tr v-for="item in datasource" :key="item.prop">-->
    <!--            <template v-for="(row, rindex) in columns" :key="rindex">-->
    <!--              <td>-->
    <!--                <el-form-item v-if="row.type==='condition'"-->
    <!--                              label=""-->
    <!--                              class="form-error-popper"-->
    <!--                              style="margin-bottom: 0 !important">-->
    <!--                  <div class="editable-cell-text">{{ getOptions(item[row.prop]) }}</div>-->
    <!--                </el-form-item>-->
    <!--                <el-form-item v-else-if="row.type==='conditionValue'"-->
    <!--                              label=""-->
    <!--                              class="form-error-popper"-->
    <!--                              style="margin-bottom: 0 !important">-->
    <!--                  <el-link type="primary" underline="never" @click="openFieldIndex(item)">-->
    <!--                    限制-->
    <!--                  </el-link>-->
    <!--                  <template v-if="setConditionValue(item['conditionKey'])">-->
    <!--                    <el-divider direction="vertical"/>-->
    <!--                    <el-link type="primary" underline="never"-->
    <!--                             @click="removeFieldIndex(item)">-->
    <!--                      取消-->
    <!--                    </el-link>-->
    <!--                    &lt;!&ndash;                    <el-divider direction="vertical"/>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                    title="申请条件限制预览"&ndash;&gt;-->
    <!--                    &lt;!&ndash;                    <ele-popover :width="320"&ndash;&gt;-->
    <!--                    &lt;!&ndash;                                 trigger="click"&ndash;&gt;-->
    <!--                    &lt;!&ndash;                                 :offset="8"&ndash;&gt;-->
    <!--                    &lt;!&ndash;                                 :popper-options="{ strategy: 'fixed' }">&ndash;&gt;-->
    <!--                    &lt;!&ndash;                      <template #reference>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                        <el-link type="primary" underline="never">&ndash;&gt;-->
    <!--                    &lt;!&ndash;                          预览&ndash;&gt;-->
    <!--                    &lt;!&ndash;                        </el-link>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                        &lt;!&ndash;                        <el-button class="ele-btn-icon">Popover</el-button>&ndash;&gt;&ndash;&gt;-->
    <!--                    &lt;!&ndash;                      </template>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                      <div>&ndash;&gt;-->
    <!--                    &lt;!&ndash;&lt;!&ndash;                        <FormPreview :currentData="currentData"&ndash;&gt;&ndash;&gt;-->
    <!--                    &lt;!&ndash;&lt;!&ndash;                                     :currentGroup="item"/>&ndash;&gt;&ndash;&gt;-->
    <!--                    &lt;!&ndash;                      </div>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                    </ele-popover>&ndash;&gt;-->
    <!--                  </template>-->
    <!--                  <div class="editable-cell-text">-->
    <!--                    &lt;!&ndash;                    {{-->
    <!--  &ndash;&gt;&#45;&#45; >-->
    <!--  < !&#45;&#45; &lt; ! & ndash;                      setConditionValue(item['conditionKey']) & ndash;&gt;&#45;&#45; >-->
    <!--  < !&#45;&#45; &lt; ! & ndash;-->
    <!--}}&ndash;&gt;-->
    <!--                  </div>-->
    <!--                </el-form-item>-->
    <!--                <div v-else class="editable-cell-text">-->
    <!--                  <ele-ellipsis :tooltip="{-->
    <!--                          original: false,-->
    <!--                          popperStyle: {-->
    <!--                            width: '320px',-->
    <!--                            background: '#ffffff',-->
    <!--                            color: '#2a2a2a',-->
    <!--                            maxWidth: 'calc(100vw - 32px)'-->
    <!--                          }-->
    <!--                        }">-->
    <!--                    {{ item[row.prop] }}-->
    <!--                  </ele-ellipsis>-->
    <!--                </div>-->
    <!--              </td>-->
    <!--            </template>-->
    <!--          </tr>-->
    <!--          <tr v-if="!datasource || !datasource.length">-->
    <!--            <td :colspan="columns.length" style="text-align: center">-->
    <!--              <ele-text style="padding: 4px 0" type="secondary">-->
    <!--                暂无数据-->
    <!--              </ele-text>-->
    <!--            </td>-->
    <!--          </tr>-->
    <!--          </tbody>-->
    <!--        </ele-table>-->
    <!--      </div>-->
    <!--    </el-form>-->
    <ConditionfFormRestrict :currentProject="currentProject"/>
    <!--    <template #footer>-->
    <!--      <el-button size="small" @click="updateModelValue(false)">取消</el-button>-->
    <!--      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">-->
    <!--        提交-->
    <!--      </el-button>-->
    <!--    </template>-->
    <!--    <FieldIndex v-model="showFieldIndex" :currentCondition="currentCondition" :currentData="currentData"-->
    <!--                :projectId="currentProject.id"-->
    <!--                @done="reLoad"/>-->
  </ele-drawer>
</template>

<script setup>
import {reactive, ref, watch} from 'vue';
import {queryCondition} from '../api/index'
import {comCondition,} from '@/utils/common_bak2.js';
import { ElMessage as EleMessage} from "element-plus";
import {
  operation as ziZhuFormTemplateFieldLinkOpt,
} from "@/views/zizhu/api/form-template-field-link.js";
import FieldIndex from './field-index.vue'
import {getFormTestrict, removes} from "@/views/zizhu/condition/api/form-restrict-index.js";
import {ElMessageBox} from "element-plus";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import ConditionfFormRestrict from "@/views/zizhu/components/setting/condition-form-restrict.vue"

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  currentProject: Object,
});

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('done');
  emit('update:modelValue', value);
};
</script>

<style lang="scss" scoped>
</style>
