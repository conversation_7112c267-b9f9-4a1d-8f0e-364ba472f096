<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <ele-tabs
          type="tag"
          size="small"
          v-model="activeName"
          :items="tabsItems"
        >
          <template #label="{ item, label }">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <div class="view-switch">
          <el-radio-group v-model="displayMode" size="small">
            <el-radio-button label="fluid">
              <el-icon>
                <Menu />
              </el-icon>
              卡片视图
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon>
                <Grid />
              </el-icon>
              表格视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <!-- 表格模式保持不变 -->
      <ele-pro-table
        v-if="displayMode === 'table'"
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
      >
        <template #xm="{ row }">
          <ele-tooltip content="审核" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>

      <!-- 流体卡片模式 - 现代科技风格 -->
      <div v-else class="modern-layout">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无申请数据" />
          </div>
          <el-row v-else :gutter="8">
            <el-col
              v-for="item in fluidData"
              :key="item.id"
              :lg="8"
              :md="12"
              :sm="12"
              :xs="24"
            >
              <div class="tech-card" @click="openEdit(item)">
                <!-- 顶部信息栏 -->
                <div class="card-header">
                  <div class="user-info">
                    <template v-if="item.userInfo">
                      <el-avatar
                        v-if="item.userInfo.photo"
                        :size="60"
                        class="user-avatar"
                        :src="
                          item.userInfo.xgh
                            ? `/api/personInfo/${item.userInfo.xgh}/photo?access_token=${accessToken}`
                            : ''
                        "
                      >
                        {{ item.userInfo?.xm?.charAt(0) || '' }}
                      </el-avatar>
                      <template v-else>
                        <el-avatar
                          v-if="item.userInfo.xb === '男'"
                          :size="60"
                          src="/male.png"
                          :alt="item.userInfo.xb"
                          class="user-avatar"
                        />
                        <el-avatar
                          v-else-if="item.userInfo.xb === '女'"
                          :size="60"
                          src="/female.png"
                          :alt="item.userInfo.xb"
                          class="user-avatar"
                        />
                      </template>
                    </template>

                    <div class="user-details">
                      <div class="student-name-row">
                        <h3 class="student-name">
                          <span class="highlight">{{
                            item.userInfo.xm || '--'
                          }}</span>
                        </h3>
                        <div class="student-meta">
                          <span>{{ item.userInfo.xgh || '--' }}</span>
                        </div>
                      </div>
                      <div class="meta-info">
                        <span class="apply-time detail-row">
                          <IconPark name="history" size="15" strokeWidth="3" />
                          <span class="span-wrap">{{
                            formatDateTime(item.sqsj)
                          }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 岗位信息卡片 -->
                <div class="job-card">
                  <div class="job-title detail-row">
                    <IconPark name="app-store" size="18" strokeWidth="3" />
                    <span class="span-wrap">{{
                      item.jobApplication?.jobName || '未知岗位'
                    }}</span>
                  </div>

                  <div class="job-details">
                    <div class="detail-row">
                      <IconPark name="local" size="16" strokeWidth="3" />
                      <span class="span-wrap">{{
                        item.jobApplication?.xqmc || '未知校区'
                      }}</span>
                    </div>

                    <div class="detail-row">
                      <IconPark name="calendar" size="16" strokeWidth="3" />
                      <span class="span-wrap">{{
                        formatDateRange(
                          item.jobApplication?.startDate,
                          item.jobApplication?.endDate
                        )
                      }}</span>
                      <IconPark name="time" size="16" strokeWidth="3" />
                      <span class="span-wrap"
                        >{{ item.jobApplication?.startTime }}~{{
                          item.jobApplication?.endTime
                        }}</span
                      >
                    </div>
                    <div class="time-grid">
                      <div class="time-item detail-row">
                        <IconPark name="plan" size="16" strokeWidth="3" />
                        <span class="span-wrap">{{
                          formatWorkDays(item.jobApplication?.workDays)
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div v-if="fluidData.length > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>

    <edit v-model="showEdit" :data="editData" @done="reloadData" />
  </ele-page>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { Grid, Menu } from '@element-plus/icons-vue';
  import { queryQgzStudentApprovalPage, getNodeState } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import Edit from './components/edit.vue';
  import { comColumns } from './utils/index.js';
  import { comApproveStatus, insertAtIndex } from '@/utils/common_bak2.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import _ from 'lodash';
  import dayjs from 'dayjs';
  import { getToken } from '@/utils/token-util.js';
  import IconPark from '@/components/IconPark/index.vue';

  const router = useRouter();
  const { query } = router.currentRoute.value;
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const accessToken = getToken();

  // 显示模式
  const displayMode = ref('fluid');

  // 数据相关
  const fluidData = ref([]);
  const loading = ref(false);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 表格相关
  const tableRef = ref(null);
  const selections = ref([]);
  const columns = ref(comColumns());

  // 标签页相关
  const activeName = ref(null);
  const tabsItems = ref([]);
  const nodeStateArray = ref([]);
  const checkedNodeId = ref(query?.currentNodeId ?? null);

  // 编辑相关
  const showEdit = ref(false);
  const editData = ref(null);

  const bodyStyle = computed(() => ({
    overflow: 'hidden',
    ...(displayMode.value === 'fluid' ? { background: '#f0f2f5' } : {}),
    ...(displayMode.value === 'fluid'
      ? { padding: '0 0 10px !important' }
      : { padding: '0 10px 10px !important' })
  }));

  // 格式化函数
  const formatDateRange = (startDate, endDate) => {
    if (!startDate || !endDate) return '未设置工作时间';
    return `${dayjs(startDate).format('YYYY-MM-DD')} 至 ${dayjs(endDate).format('YYYY-MM-DD')}`;
  };

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '未知时间';
    return dayjs(dateTime).format('YYYY-MM-DD HH:mm');
  };

  const formatWorkDays = (workDays) => {
    if (!workDays) return '未设置工作日';
    const dayMap = {
      1: '周一',
      2: '周二',
      3: '周三',
      4: '周四',
      5: '周五',
      6: '周六',
      7: '周日'
    };
    return (typeof workDays === 'string' ? workDays.split(',') : workDays)
      .map((day) => dayMap[day])
      .join('、');
  };

  // 查询节点状态
  const queryNodeState = async () => {
    try {
      const data = await getNodeState({ moduleCode: 'qgzx_sqgw' });
      if (data) {
        nodeStateArray.value = data.map((item) => ({
          ...item,
          value: item.nodeId
        }));
        checkedNodeId.value = query?.currentNodeId || data[0]?.nodeId;
        handleDialogSubmit(query?.currentResult);
      }
    } catch (e) {
      ElMessage.error(e.message);
    }
  };

  // 初始化加载
  onMounted(queryNodeState);

  // 处理对话框提交
  const handleDialogSubmit = (presetResult = null) => {
    if (!checkedNodeId.value) {
      ElMessage.error('请选择你要审核的节点信息');
      return;
    }

    const checkedNode = nodeStateArray.value.find(
      (item) => item.nodeId === checkedNodeId.value
    );
    let baseItems = comApproveStatus();

    baseItems.forEach((item) => {
      item.label = checkedNode[item.diyname];
      item.name = checkedNode[item.prename];
    });

    insertAtIndex(baseItems, { label: '待审批', name: '待审批' }, 0);
    activeName.value = presetResult || baseItems[0].name;
    tabsItems.value = baseItems;
  };

  // 获取数据（防抖）
  const fetchData = _.debounce(async () => {
    if (!activeName.value) return;
    try {
      loading.value = true;
      const res = await queryQgzStudentApprovalPage({
        page: currentPage.value,
        limit: pageSize.value,
        result: activeName.value
      });
      fluidData.value = res.list || [];
      total.value = res.count || 0;
    } catch (e) {
      ElMessage.error(e.message);
    } finally {
      loading.value = false;
    }
  }, 300);

  // 监听变化
  watch(
    [displayMode, activeName],
    ([newMode, newName]) => {
      currentPage.value = 1;
      if (newName)
        newMode === 'fluid' ? fetchData() : tableRef.value?.reload?.();
    },
    { immediate: false }
  );

  // 分页处理
  const handlePageChange = () => displayMode.value === 'fluid' && fetchData();
  const handleSizeChange = (size) => {
    pageSize.value = size;
    displayMode.value === 'fluid' && fetchData();
  };

  // 编辑相关
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };
  const reloadData = () => {
    currentPage.value = 1;
    displayMode.value === 'fluid' ? fetchData() : tableRef.value?.reload?.();
  };

  // 表格数据源
  const datasource = ({ page, limit }) => {
    if (!activeName.value) return Promise.resolve({ data: [], total: 0 });
    return queryQgzStudentApprovalPage({
      page,
      limit,
      result: activeName.value
    });
  };
</script>

<style scoped>
  /* 现代科技风格布局 */
  .modern-layout {
    background: transparent;
    padding: 8px;
  }

  .loading-container {
    padding: 20px;
  }

  .empty-container {
    padding: 40px 0;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }

  /* 科技感卡片设计 */
  .tech-card {
    border: 1px solid rgba(0, 0, 0, 0.05);
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    -webkit-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    margin-bottom: 16px;
    cursor: pointer;
  }

  .tech-card:hover {
    -webkit-transform: translateY(-4px);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: rgba(99, 102, 241, 0.2);
  }

  /* 卡片头部 */
  .card-header {
    padding: 6px 8px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
  }

  .user-info {
    display: flex;
    align-items: center;
  }

  .user-avatar {
    margin-left: 10px;
    margin-right: 12px;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  }

  /* 修改用户信息区域样式 */
  .user-details {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
  }

  .student-name-row {
    display: flex;
    align-items: center;
    gap: 6px; /* 减小间距 */
    margin-bottom: 4px; /* 添加下边距 */
  }

  .student-name {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .student-meta {
    padding-top: 5px;
    font-size: 12px;
    color: #64748b;
    white-space: nowrap;
  }

  .meta-info {
    margin: 4px 0 0; /* 减小上边距 */
    color: #555;
    font-size: 13px;

    .apply-time {
      display: inline-flex; /* 改为行内布局 */
      align-items: center;
    }
  }

  /* 岗位信息卡片 */
  .job-card {
    padding: 10px 16px;
    background: white;
  }

  .job-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 15px;
    font-weight: 600;
    color: #34495e;
  }

  .job-details {
    margin-top: 12px;
  }

  .detail-row {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    color: #555;
  }

  .detail-row .span-wrap {
    padding-left: 10px;
    margin-right: 8px;
    color: #555;
    font-size: 14px;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
  }

  .time-grid {
    margin-top: 8px;
  }

  .time-item {
    display: -webkit-box; /* 兼容旧版 QQ 浏览器 */
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    padding: 6px 8px 6px 0;
    -webkit-border-radius: 6px;
    border-radius: 6px;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .time-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
