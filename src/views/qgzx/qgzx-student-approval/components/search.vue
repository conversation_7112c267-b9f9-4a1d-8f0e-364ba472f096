<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '8px 0 0px 0px !important',borderTop: '1px dashed #ebeef5' }">
    <el-form ref="formRef"
             size="small"
             @keyup.enter="search"
             @submit.prevent=""
             label-width="auto">
      <el-row :gutter="8">
        <template v-for="(item, index) in initItems">
          <template v-if="index > 2">
            <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="12">
              <ProFormItem v-if="searchExpand"
                           :item="item"
                           :model="initModel"
                           @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)">
                <template
                  v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
                )"
                  #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"></slot>
                </template>
              </ProFormItem>
            </el-col>
          </template>
          <el-col v-else :lg="6" :md="12" :sm="12" :xs="12">
            <ProFormItem :item="item"
                         :model="initModel"
                         @updateItemValue="
              (prop, value) => updateFormValue(item, prop, value)">
              <template v-for="name in Object.keys($slots).filter(
                (k) =>
                  !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
              )"
                        #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
        <el-col :lg="6" :md="12" :sm="12" :xs="12">
          <el-form-item>
            <div :style="{
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }">
              <div>
                <el-button type="primary" size="small" plain @click="search">查询</el-button>
                <el-button size="small" @click="reset">重置</el-button>
                <el-link type="primary"
                         underline="never"
                         @click="searchExpand = !searchExpand"
                         style="margin-left: 10px">
                  <template v-if="searchExpand">
                    <span>收起</span>
                    <el-icon style="vertical-align: -1px">
                      <ArrowUp/>
                    </el-icon>
                  </template>
                  <template v-else>
                    <span>展开</span>
                    <el-icon style="vertical-align: -2px">
                      <ArrowDown/>
                    </el-icon>
                  </template>
                </el-link>
              </div>
              <!-- 工具栏 - 紧凑布局 -->
              <div class="toolbar-row" style="margin-left: 16px;">
                <el-tooltip content="卡片列表" placement="top" effect="light">
                  <IconPark
                    name="view-grid-card"
                    size="22"
                    strokeWidth="3"
                    :style="{ color: displayMode === 'fluid' ? '#409eff' : '' }"
                    @click="changeDisplayMode('fluid')"
                  />
                </el-tooltip>
                <el-tooltip content="表格列表" placement="top" effect="light">
                  <IconPark
                    name="view-grid-list"
                    size="22"
                    strokeWidth="3"
                    :style="{ color: displayMode === 'table' ? '#409eff' : '' ,marginLeft: '8px'}"
                    @click="changeDisplayMode('table')"
                  />
                </el-tooltip>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import {nextTick, ref, unref, defineEmits, defineProps} from 'vue';
import {ArrowDown, ArrowUp} from '@/components/icons';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import {generateForm} from '@/utils/common_bak2.js';
import {comColumns} from '@/views/qgzx/qgzx-job-apply/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';

const emit = defineEmits(['search', 'change-display-mode', 'open-edit', 'handle-delete']);
const props = defineProps({
  displayMode: {
    type: String,
    default: 'fluid'
  },
  selections: {
    type: Array,
    default: () => []
  }
});

const formRef = ref(null);
const initItems = ref([]);
const initModel = ref({});
const showSet = ref(false);
const searchExpand = ref(false);

const search = () => emit('search', {...initModel.value});

const reset = () => {
  nextTick(() => {
    formRef.value?.clearValidate?.();
    formRef.value?.resetFields?.();
    initModel.value = {};
    search();
  });
};

const updateFormValue = (item, prop, value) => {
  initModel.value[prop] = value;
};

const changeDisplayMode = (mode) => {
  emit('change-display-mode', mode);
};

const openEdit = (row) => {
  emit('open-edit', row);
};

const handleDelete = () => {
  emit('handle-delete');
};

const initFieldList = async () => {
  const baseColumns = comColumns() || [];
  // 过滤出有 prop 和 marker 为 'search' 的字段，并设置 required 为 false
  initItems.value = baseColumns.filter(item => item.prop && item.marker === 'search').map(item => ({
    ...item,
    required: false, // 强制设置为 false
  }));
  initItems.value.push({
    prop: 'sqsj',
    label: '申请时间',
    type: 'daterange',
    required: false,
  });
  console.log(initItems.value);
  initModel.value = generateForm(initItems.value);
  console.log('初始化搜索字段:', initItems.value);
};

initFieldList();
defineExpose({initModel});
</script>

<style scoped>
.toolbar-row {
  display: flex;
  align-items: center;

  .iconpark-icon {
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.1);
    }

    &:first-child {
      margin-left: 0;
    }
  }

  .el-button {
    margin-left: 8px;
  }
}
</style>
