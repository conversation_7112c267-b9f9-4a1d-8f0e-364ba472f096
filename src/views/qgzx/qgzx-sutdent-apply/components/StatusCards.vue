<template>
  <div class="status-cards-container">
    <el-row :gutter="8">
      <el-col
        v-for="item in statusItems"
        :key="item.value"
        :xs="24"
        :sm="12"
        :md="6"
        :lg="6"
      >
        <el-card :body-style="{ padding: '10px' }"
          shadow="hover"
          :class="['status-card', { 'active': activeTab === item.value }]"
          @click="handleClick(item.value)"
        >
          <div class="card-content">
            <div class="status-info">
              <div class="status-name">{{ item.label }}</div>
              <div class="status-count">{{ item.count || 0 }}</div>
            </div>
            <div class="status-icon">
              <el-icon :size="28" :color="item.color">
                <component :is="item.icon" />
              </el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  Clock,
  CircleCheck,
  CircleClose,
  Refresh
} from '@element-plus/icons-vue'

const props = defineProps({
  activeTab: {
    type: String,
    default: '待审批'
  }
})

const emit = defineEmits(['tab-change'])

// 状态数据
const statusItems = ref([
  {
    label: '可申请岗位',
    value: 'keshenqing',
    color: '#67C23A',
    icon: CircleCheck,
    count: 5
  },
  {
    label: '已申请岗位',
    value: 'yishenqing',
    color: '#409EFF',
    icon: Clock,
    count: 1
  },
  {
    label: '待面试岗位',
    value: '待面试岗位',
    color: '#F56C6C',
    icon: CircleClose,
    count: 1
  },
  {
    label: '待签到岗位',
    value: '待签到岗位',
    color: '#E6A23C',
    icon: Refresh,
    count: 0
  }
])

// 加载统计数据
const loadStats = async () => {
  // try {
  //   const res = await getJobStats()
  //   statusItems.value.forEach(item => {
  //     item.count = res[item.value] || 0
  //   })
  // } catch (error) {
  //   console.error('加载统计数据失败:', error)
  // }
}

// 点击卡片切换状态
const handleClick = (tab) => {
  emit('tab-change', tab)
}

// 初始化加载数据
onMounted(() => {
  loadStats()
})

// 暴露刷新方法
defineExpose({
  refresh: loadStats
})
</script>

<style scoped lang="scss">
.status-cards-container {
  .status-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0!important;

    &.active {
      border-left: 4px solid transparent;
      border-left-color: var(--el-color-primary);
      background-color: rgba(64, 158, 255, 0.05);
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-info {
        .status-name {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 3px;
        }

        .status-count {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
        }
      }

      .status-icon {
        opacity: 0.8;
      }
    }
  }
}
</style>
