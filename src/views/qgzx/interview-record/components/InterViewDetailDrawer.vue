<template>
  <ele-drawer
    size="70%"
    title="详情"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <StudentHeader v-if="userInfo"
                   routeType="student"
                   :current-xgh="userInfo.xgh"/>
    <!-- 主要内容区域 -->
    <div class="modern-job-detail">
      <div class="modern-content">
        <!-- 学生申请基本信息卡片 -->
        <div class="modern-card modern-main-info">
          <div class="modern-card-header">
            <div class="modern-card-icon">
              <IconPark name="schedule" size="18" strokeWidth="3" theme="filled"/>
            </div>
            <h3>学生申请信息</h3>
            <div class="status-badge" :class="getStatusClass(studentApplyInfo.spzt)">
              {{ studentApplyInfo.spzt || '-' }}
            </div>
          </div>
          <student-apply-info-views :form-model="studentApplyInfo"/>
        </div>
      </div>
    </div>
    <JobDetailViews :form-model="qgzJobInfo"
                    enter-type="views"
                    class="drawer-content"/>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';
import JobDetailViews from '@/views/qgzx/components/JobDetailViews.vue';
import IconPark from '@/components/IconPark/index.vue';
import {getStatusClass} from '@/views/qgzx/utils/index.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import StudentApplyInfoViews from '@/views/qgzx/components/studentApplyInfoViews.vue';

const emit = defineEmits(['done', 'update:modelValue']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const props = defineProps({
  modelValue: Boolean,
  data: Object,
  enterType: String,//studentJob=学生角色，已面试
});

// 状态管理
const loading = ref(false);
const formModel = ref({});
const studentApplyInfo = ref({});
const userInfo = ref({});
const approvalData = ref(null);
const proFormGroup = ref([]);
const qgzJobInfo = ref(null);
const activities = ref([]);

const updateModelValue = (value) => {
  proFormGroup.value = null;
  emit('update:modelValue', value);
};

// 初始化
watch(() => props.modelValue, async (modelValue) => {
  if (modelValue && props.data) {
    userInfo.value = props.data.userInfo;
    studentApplyInfo.value = props.data.studentApply;
    qgzJobInfo.value = {
      ...props.data.jobApplication,
      interviewResult: props.data.interviewResult,
      interviewComment: props.data.interviewComment,
      workDays: typeof props.data?.jobApplication.workDays === 'string'
        ? props.data.jobApplication.workDays.split(',')
        : Array.isArray(props.data?.jobApplication.workDays)
          ? props.data.jobApplication.workDays
          : [],
      Addresses: props.data?.jobApplication.jobAddresses?.[0]?.name || '-',
    };

    approvalData.value = {
      groupName: '审核信息',
      title: '',
      id: props.data.id,
      sqId: props.data.id,
      nodeId: props.data.nodeId,
      infoType: 'QgzxStudentApproval',
      workflowId: props.data.workflowId,
      listFlag: '否',
    };
  }
}, {immediate: true});
</script>

<style lang="less" scoped>
@import "../../css/qgzx-job-detail.less";

.modern-info-item {
  margin-bottom: 0.5rem;
}
</style>
