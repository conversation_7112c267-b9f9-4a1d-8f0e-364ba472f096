<template>
  <div class="modern-job-drawer">
    <!-- 固定顶部操作栏 -->
    <div class="fixed-header">
      <div class="top-bar">
        <!-- 返回按钮 -->
        <div class="back-btn" @click="onBack">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <span class="back-text">返回</span>
        </div>
        <!-- 查询表单 -->
        <div class="search-form">
          <el-form :inline="true" size="small">
            <el-form-item label="姓名">
              <el-input
                v-model="searchQuery.studentName"
                placeholder="搜索姓名"
                clearable
                class="modern-input"/>
            </el-form-item>
            <el-form-item label="学号">
              <el-input
                v-model="searchQuery.xgh"
                placeholder="搜索学号"
                clearable
                class="modern-input"/>
            </el-form-item>
            <el-form-item label="面试状态">
              <el-select
                v-model="searchQuery.interviewResult"
                clearable
                placeholder="请选择面试状态"
                style="width: 160px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="skeleton-grid">
          <div v-for="i in 6" :key="i" class="skeleton-card">
            <el-skeleton :rows="3" animated :loading="loading"/>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="fluidData.length === 0" class="empty-container">
        <el-empty description="暂无面试数据">
          <template #image>
            <div class="empty-illustration">
              <svg width="200" height="150" viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M50 50H150V100H50V50Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
                <circle cx="75" cy="75" r="15" fill="#E4E7ED"/>
                <rect x="100" y="65" width="40" height="20" rx="2" fill="#E4E7ED"/>
                <path d="M30 120H170V130H30V120Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
              </svg>
            </div>
          </template>
          <el-button type="primary" @click="resetFilters">重新加载</el-button>
        </el-empty>
      </div>

      <!-- 数据展示 -->
      <template v-else>
        <div class="card-grid">
          <div
            v-for="item in fluidData"
            :key="item.id"
            class="tech-card"
            :class="[getStatusClass(item.interviewResult)]"
          >
            <!-- 学生信息卡片 -->
            <div class="student-card" @click="openDetail(item)">
              <div class="avatar-container">
                <template v-if="item.userInfo">
                  <el-avatar
                    v-if="item.userInfo.photo"
                    :size="60"
                    class="user-avatar"
                    :src="item.userInfo.xgh ? `/api/personInfo/${item.userInfo.xgh}/photo?access_token=${accessToken}` : ''">
                    {{ item.userInfo?.xm?.charAt(0) || '' }}
                  </el-avatar>
                  <template v-else>
                    <el-avatar
                      v-if="item.userInfo.xb === '男'"
                      :size="60"
                      src="/male.png"
                      :alt="item.userInfo.xb"
                      class="user-avatar"/>
                    <el-avatar
                      v-else-if="item.userInfo.xb === '女'"
                      :size="60"
                      src="/female.png"
                      :alt="item.userInfo.xb"
                      class="user-avatar"/>
                  </template>
                </template>
                <div class="status-badge" :class="getStatusClass(item.interviewResult)">
                  {{ item.interviewResult || '待安排' }}
                </div>
              </div>

              <div class="student-info">
                <h3 class="student-name">
                  {{ item.userInfo.xm || '--' }}
                  <span class="student-id">{{ item.userInfo.xgh || '--' }}</span>
                </h3>

                <div class="contact-info">
                  <div class="contact-item">
                    <el-icon>
                      <Phone/>
                    </el-icon>
                    <span>{{ item.userInfo.sjh || '未提供' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 面试信息 -->
            <div class="info-section interview-info" @click="openDetail(item)">
              <div class="section-title">
                <el-icon>
                  <Clock/>
                </el-icon>
                <span>面试信息</span>
              </div>
              <div class="info-content">
                <div class="info-row">
                  <span class="info-label">时间：</span>
                  <span class="info-value">{{
                      item.interviewTime ? formatDateTime(item.interviewTime) : '待安排'
                    }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">地点：</span>
                  <span class="info-value">{{ item.interviewLocation || '待安排' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">申请时间：</span>
                  <span class="info-value">{{ formatDate(item.createTime) }}</span>
                </div>
              </div>
            </div>

            <!-- 岗位信息 -->
            <div class="info-section job-info" @click="openDetail(item)">
              <div class="section-title">
                <el-icon>
                  <Briefcase/>
                </el-icon>
                <span>岗位信息</span>
              </div>
              <div class="info-content">
                <div class="info-row">
                  <span class="info-label">岗位：</span>
                  <span class="info-value">{{ item.jobName || '未知岗位' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">部门：</span>
                  <span class="info-value">{{ item.employerName || '未知部门' }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button size="small"
                         type="primary"
                         plain
                         @click.stop="handlePass(item,'yes')"
                         v-if="item.interviewResult === '待面试'">
                通过
              </el-button>
              <el-button size="small"
                         type="danger"
                         plain
                         @click.stop="handlePass(item,'no')"
                         v-if="item.interviewResult === '待面试'">
                不通过
              </el-button>
              <el-button size="small"
                         type="warning"
                         plain
                         @click.stop="handleReject(item)"
                         v-if="item.interviewResult === '待面试'">
                缺席
              </el-button>
              <el-button size="small"
                         type=""
                         plain
                         @click.stop="openDetail(item)">
                详情
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="fluidData.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="fluidData.length"
            :page-sizes="[12, 24, 36, 48]"
            layout="total, sizes, prev, pager, next"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
            small
            background
          />
        </div>
      </template>
    </div>

    <InterViewDetailDrawer v-model="detailVisible" :data="detailData"/>
  </div>
</template>


<script setup>
import {computed, onMounted, ref, unref, watch} from 'vue';
import {queryQgzxInterviewRecordEmployerPage} from '@/views/qgzx/interview-record/api/index.js';
import {ElMessage, ElMessageBox} from 'element-plus';
import {
  Search,
  Refresh,
  Briefcase,
  Clock,
} from '@element-plus/icons-vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {operation} from './api/index.js';
import InterViewDetailDrawer from '@/views/qgzx/interview-record/components/InterViewDetailDrawer.vue';
import {getToken} from '@/utils/token-util.js';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import IconPark from '@/components/IconPark/index.vue';

const emit = defineEmits(['update:modelValue', 'apply']);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const accessToken = getToken();
// 路由相关
const {removePageTab, getRouteTabKey} = usePageTab();
const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
// 路由参数解析
const routeParams = computed(() => ({
  activeName: query?.activeName ?? '',
}));
const pathArray = path.split('/');
const currentJobId = pathArray[3];

console.log(routeParams.value, currentJobId);
// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(12);
const options = [
  {
    value: '待面试',
    label: '待面试',
  },
  {
    value: '已通过',
    label: '已通过',
  },
  {
    value: '未通过',
    label: '未通过',
  },
];
const searchQuery = ref({
  studentName: '',
  xgh: '',
  interviewResult: '',
});

const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  jobId: currentJobId,
  ...searchQuery.value,
}));

// 返回上一页
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/qgzx/qgzx-job-apply',
    query: {currentActiveName: routeParams.value.activeName},
  });
};

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await queryQgzxInterviewRecordEmployerPage(queryParams.value);
    fluidData.value = res.list || [];
  } catch (e) {
    ElMessage.error(e.message || '获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索和日期变化时重置
const handleSearch = () => {
  currentPage.value = 1;
  fetchData();
};
const resetSearch = () => {
  searchQuery.value = {
    studentName: '',
    xgh: '',
    interviewResult: '',
  };
  currentPage.value = 1;
  fetchData();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  window.scrollTo({top: 0, behavior: 'smooth'});
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const formatDate = (dateString) => {
  if (!dateString) return '--';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'});
};

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '--';
  const date = new Date(dateTimeString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }).replace(/\//g, '-');
};

const getStatusClass = (status) => {
  switch (status) {
    case '待面试':
      return 'status-pending';
    case '通过':
      return 'status-passed';
    case '不通过':
      return 'status-failed';
    default:
      return 'status-quexi';
  }
};
const detailVisible = ref(false);
const detailData = ref(null);
const openDetail = (item) => {
  detailData.value = item;
  detailVisible.value = true;
};

const handlePass = (item, mark) => {
  if (item.interviewResult === '待面试') {
    ElMessageBox.prompt(`确定要 ${mark === 'yes' ? '通过' : '拒绝 '} ${item.userInfo.xm} 的面试吗? 请输入备注`,
      `面试结果设置`, {
        confirmButtonText: '确定',
        cancelButtonText: '关闭',
        inputPattern: /.{5,}/,
        inputErrorMessage: '至少输入5个字符',
      }).then(({value}) => {
      let data = {
        id: item.id,
        interviewResult: mark === 'yes' ? '通过' : '不通过',
        interviewComment: value,
      };
      if (mark === 'yes') {
        operation(data).then(() => {
          ElMessage.success('面试结果设置成功');
          fetchData(); // 操作成功后刷新数据
        }).catch(error => {
          ElMessage.error('操作失败: ' + (error.message || '未知错误'));
        });
      } else {
        ElMessageBox.confirm(`确定要拒绝 ${item.userInfo.xm} 的申请吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          operation(data).then(() => {
            ElMessage.success('申请已拒绝');
            fetchData(); // 操作成功后刷新数据
          }).catch(error => {
            ElMessage.error('操作失败: ' + (error.message || '未知错误'));
          });
        });
      }
    }).catch(() => {
    });
  }
};

const handleReject = (item) => {
  ElMessageBox.confirm(
    `你确定要把 ${item.userInfo.xm} 的面试标记为缺席吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    let data = {
      id: item.id,
      interviewResult: '缺席',
    };
    operation(data).then(() => {
      ElMessage.success('申请已标记为缺席');
      fetchData(); // 操作成功后刷新数据
    }).catch(error => {
      ElMessage.error('操作失败: ' + (error.message || '未知错误'));
    });
  }).catch(() => {});
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

// 页面初始化
onMounted(() => {
  fetchData();

});

</script>

<style lang="scss" scoped>
.modern-job-drawer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

/* 固定顶部区域 */
.fixed-header {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 8px 8px 1px 8px;
  background-color: #f0f2f5;
  flex-shrink: 0; /* 防止顶部栏被压缩 */
}

/* 内容区域 - 关键修改 */
.content-wrapper {
  flex: 1;
  overflow-y: auto; /* 只允许内容区域滚动 */
  padding: 0 8px 8px;
  //margin-bottom: 8px;
}

/* 顶部操作栏 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}


/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
  transition: color 0.3s;
}

.back-btn:hover {
  color: #409eff;
}

.back-text {
  margin-left: 6px;
  font-size: 14px;
}

/* 查询表单 */
.search-form {
  margin-top: 10px;
  flex: 1;
  display: flex;
  justify-content: flex-end;
}


/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  background-color: #f5f7fa;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 8px;
  margin-bottom: 8px;
}

/* 卡片样式 */
.tech-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }

  &.status-pending {
    border-left-color: #f59e0b;
  }

  &.status-passed {
    border-left-color: #10b981;
  }

  &.status-failed {
    border-left-color: #ef4444;
  }
}

.student-card {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e4e7ed;
}

.avatar-container {
  position: relative;

  .user-avatar {
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .status-badge {
    position: absolute;
    bottom: -4px;
    right: -4px;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
    color: white;

    &.status-pending {
      background: #f59e0b;
    }

    &.status-passed {
      background: #10b981;
    }

    &.status-failed {
      background: #ef4444;
    }
  }
}

.student-info {
  flex: 1;

  .student-name {
    font-size: 16px;
    margin: 0 0 4px 0;
    color: #303133;

    .student-id {
      font-size: 13px;
      color: #909399;
      margin-left: 8px;
    }
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .contact-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      color: #606266;
    }
  }
}

.info-section {
  margin-bottom: 12px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #409eff;
    margin-bottom: 8px;
  }

  .info-content {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .info-row {
      display: flex;
      font-size: 13px;

      .info-label {
        color: #909399;
        min-width: 60px;
      }

      .info-value {
        color: #303133;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;

  .el-button {
    flex: 1;
  }
}

/* 加载状态 */
.loading-container {
  padding: 20px;

  .skeleton-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;

    .skeleton-card {
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 分页 */
.pagination-container {
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-radius: 8px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .top-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .search-form {
    width: 100%;

    .el-form {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .card-grid {
    grid-template-columns: 1fr;
  }
}
</style>
