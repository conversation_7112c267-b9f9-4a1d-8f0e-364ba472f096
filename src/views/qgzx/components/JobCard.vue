<template>
  <ele-card :style="{margin:'4px 0!important'}"
            :body-style="{ padding: '0 3px!important', cursor: 'pointer', overflow: 'hidden' }">
    <div class="list-item-body">
      <div style="flex: 1">
        <div @click.stop="$emit('detail', data)">
          <div style="display: flex; justify-content: space-between; align-items: center">
            <ele-text type="primary" size="md">
              <IconPark name="app-store" size="18" strokeWidth="3"/>
              {{ data.jobName }}
            </ele-text>
            <el-tag v-if="data.jobTypeId" size="small" effect="light">
              {{ jobTypeName }}
            </el-tag>
          </div>
          <div style="margin-top: 3px; display: flex; align-items: center">
            <el-tag v-for="tag in data.tags"
                    :key="tag"
                    type="info"
                    size="small"
                    :disable-transitions="true"
                    style="margin-right: 8px">
              {{ tag }}
            </el-tag>
          </div>
          <div class="description-container">
            <ele-ellipsis style="font-weight: unset;"
                          :content="data.bz || '暂无描述'"
                          :max-line="1"
                          :tooltip="{
              effect: 'light',
              placement: 'top',
              popperStyle: {
                width: '380px',
                maxWidth: '90%',
                wordBreak: 'break-all',
              },
              bodyStyle: {
                maxWidth: 'calc(100vw - 32px)',
                maxHeight: '252px',
                overflowY: 'auto',
              },
              offset: 4,
            }">
              {{ data.bz || '暂无描述' }}
            </ele-ellipsis>
          </div>
          <div class="job-details">
            <div class="detail-row">
              <IconPark name="local" size="16" strokeWidth="3"/>
              <span class="span-wrap">{{ data.xqmc || '未知校区' }} {{ getAddress(data.jobAddresses) }}</span>
            </div>
            <div class="detail-row">
              <IconPark name="calendar" size="16" strokeWidth="3"/>
              <span class="span-wrap">{{ formatDateRange(data?.startDate, data?.endDate) }}</span>
            </div>
            <div class="detail-row">
              <IconPark name="time" size="16" strokeWidth="3"/>
              <span class="span-wrap">{{ data?.startTime }}~{{ data?.endTime }}</span>
            </div>
            <div class="time-grid">
              <div class="time-item detail-row">
                <IconPark name="plan" size="16" strokeWidth="3"/>
                <span class="span-wrap">{{ formatWorkDays(data?.workDays) }}</span>
              </div>
            </div>
          </div>
          <div style="margin-top: 10px; display: flex; align-items: center; flex-wrap: wrap; gap: 4px">
            <ele-tooltip content="用工人数" placement="top" effect="light">
              <div class="info-item">
                <el-icon>
                  <User/>
                </el-icon>
                <span>{{ data.ygrs }}人</span>
              </div>
            </ele-tooltip>

            <ele-tooltip content="每月工作小时数" placement="top" effect="light">
              <div class="info-item">
                <el-icon>
                  <Timer/>
                </el-icon>
                <span>{{ data.workHous }}小时</span>
              </div>
            </ele-tooltip>

            <ele-tooltip content="时薪" placement="top" effect="light">
              <div class="info-item">
                <el-icon>
                  <Wallet/>
                </el-icon>
                <span>{{ data.hourlyRate }}元/时</span>
              </div>
            </ele-tooltip>

            <ele-tooltip content="月最高报酬" placement="top" effect="light">
              <div class="info-item">
                <el-icon>
                  <PriceTag/>
                </el-icon>
                <span>{{ data.yzgbc }}元/月</span>
              </div>
            </ele-tooltip>
          </div>
        </div>

        <!-- 优化后的操作按钮区域 -->
        <div class="action-area" v-if="enterType === 'apply'||enterType==='yishenqing'">
          <!-- 用人单位操作按钮 -->
          <template v-if="enterType === 'apply'">
            <!-- 待审批状态 -->
            <template v-if="data.spzt === '待审批'">
              <el-tooltip content="编辑岗位信息" placement="top" effect="light">
                <el-button size="small" class="action-btn" @click.stop="emit('edit', data)">编辑</el-button>
              </el-tooltip>
              <el-tooltip content="删除此岗位" placement="top" effect="light">
                <el-button size="small" class="action-btn" @click.stop="emit('delete', data)">删除</el-button>
              </el-tooltip>
            </template>
            <!-- 审批通过状态 -->
            <template v-else-if="data.spzt === '通过'">
              <el-tooltip v-if="data.sfms === '是'" content="管理面试记录" placement="top" effect="light">
                <el-button size="small" class="action-btn" plain @click.stop="emit('interview', data)">面试</el-button>
              </el-tooltip>
              <el-tooltip content="考勤管理" placement="top" effect="light">
                <el-button size="small" class="action-btn" plain @click.stop="emit('attendance', data)">考勤管理
                </el-button>
              </el-tooltip>
              <el-tooltip content="薪酬管理" placement="top" effect="light">
                <el-button size="small" class="action-btn" plain @click.stop="emit('salary', data)">薪酬管理</el-button>
              </el-tooltip>
            </template>
            <!-- 其他状态（驳回等） -->
            <template v-else>
              <el-tooltip content="重新编辑提交" placement="top" effect="light">
                <el-button size="small" class="action-btn" type="primary" plain @click.stop="emit('edit', data)">编辑
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除此申请" placement="top" effect="light">
                <el-button size="small" class="action-btn" plain @click.stop="emit('delete', data)">删除</el-button>
              </el-tooltip>
            </template>
          </template>

          <!-- 学生操作按钮 -->
          <template v-else-if="enterType === 'yishenqing' && data.spzt === '通过'&& data.ygzt === '用工'">
            <!--            <el-tooltip v-if="data.sfms === '是'" content="管理面试记录" placement="top" effect="light">-->
            <!--              <el-button size="small" class="action-btn" plain @click.stop="emit('interview', data)">面试</el-button>-->
            <!--            </el-tooltip>-->
            <el-tooltip content="考勤管理" placement="top" effect="light">
              <el-button size="small" class="action-btn" plain @click.stop="emit('attendance', data)">考勤管理
              </el-button>
            </el-tooltip>
            <el-tooltip content="薪酬管理" placement="top" effect="light">
              <el-button size="small" class="action-btn" plain @click.stop="emit('salary', data)">薪酬管理</el-button>
            </el-tooltip>
          </template>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import {Timer, Wallet, PriceTag, User} from '@element-plus/icons-vue';
import {formatDateRange, formatWorkDays, getAddress, getJobTypeName} from '@/views/qgzx/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';
import {ref, watchEffect} from 'vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  enterType: {
    type: String,
    default: 'apply',
    validator: (value) => ['apply', 'stuApply', 'keshenqing', 'yishenqing'].includes(value),//keshenqing-学生可申请岗位，yishenqing-学生已申请岗位
  },
});

const emit = defineEmits(['edit', 'detail', 'attendance', 'interview', 'salary', 'delete']);

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
const jobTypeName = ref('');

watchEffect(async () => {
  if (props.data?.jobTypeId) {
    jobTypeName.value = await getJobTypeName(props.data.jobTypeId);
  }
});
</script>

<style lang="less" scoped>

/* 统一字体基准 */
.ele-card {
  font-size: 14px; /* 设置基础字体大小 */
}

/* 卡片内容区域 */
.list-item-body {
  padding:6px 10px;
  font-size: inherit; /* 继承基础字体大小 */

  .ele-text {
    font-size: 15px; /* 标题稍大 */
    font-weight: 600;
  }
}

/* 信息项统一样式 */
.info-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 13px; /* 固定小号字体 */
  color: #555;
  margin-right: 8px;
  margin-bottom: 8px;

  .el-icon {
    font-size: 14px; /* 图标与文字对齐 */
  }
}

/* 操作按钮区域 */
.action-area {
  margin-top: 3px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding-top: 12px;
  border-top: 1px dashed #eee;

  .action-btn {
    font-size: 13px; /* 按钮字体统一 */
    padding: 6px 12px;
  }
}

/* 描述文本 */
.description-container {
  margin: 10px 0;
  font-size: 12px; /* 与info-item保持一致 */
  line-height: 1.4;
}

/* 详情行 */
.job-details {
  margin-top: 12px;

  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px; /* 统一字体 */
    color: #666;

    i {
      margin-right: 8px;
      font-size: 15px; /* 图标稍大 */
    }

    .span-wrap {
      flex: 1;
      font-size: 13px; /* 继承父级 */
      margin-left: 5px;
    }
  }
}

/* 标签样式统一 */
.el-tag {
  font-size: 12px !important; /* 强制统一标签字体 */
  height: 24px;
  line-height: 22px;
  margin-right: 8px;

  &--small {
    font-size: 11px !important;
    height: 22px;
    line-height: 20px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .list-item-body {
    padding: 10px;
  }

  .info-item {
    font-size: 12px;
    padding: 4px 8px;
  }

  .action-area {
    gap: 4px;

    .action-btn {
      font-size: 12px;
      padding: 5px 10px;
    }
  }
}

/* QQ浏览器字体修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .ele-card,
  .info-item,
  .description-container,
  .detail-row {
    font-size: 14px !important;
    -webkit-text-size-adjust: 100% !important;
  }
}
</style>
