<template>
  <div class="modern-job-detail">
    <!-- 主要内容区域 -->
    <div class="modern-content">
      <!-- 基本信息卡片 -->
      <div class="modern-card modern-main-info">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="book-one" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>岗位基本信息</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-info-grid">
            <div class="modern-info-item">
              <label>岗位类别</label>
              <div class="modern-info-value">{{ jobTypeName }}</div>
            </div>
            <div class="modern-info-item">
              <label>工作时间</label>
              <div class="modern-info-value">{{ formModel.startTime }} - {{ formModel.endTime }} ({{
                  formatWorkDays(formModel.workDays)
                }})
              </div>
            </div>
            <div class="modern-info-item">
              <label>工作周期</label>
              <div class="modern-info-value">{{ formModel.startDate }} 至 {{ formModel.endDate }}</div>
            </div>
            <div class="modern-info-item">
              <label>工作地点</label>
              <div class="modern-info-value">{{ formModel.xqmc }} {{ formModel.Addresses || '图书馆' }}</div>
            </div>
            <div class="modern-info-item">
              <label>薪资待遇</label>
              <div class="modern-info-value">{{ formModel.hourlyRate }}元/小时 (最高{{ formModel.yzgbc }}元/月)</div>
            </div>
            <div class="modern-info-item">
              <label>用工人数</label>
              <div class="modern-info-value">{{ formModel.ygrs }}人</div>
            </div>
            <div class="modern-info-item">
              <label>联系方式</label>
              <div class="modern-info-value">{{ formModel.lxfs }}</div>
            </div>
            <div class="modern-info-item">
              <label>每月工时</label>
              <div class="modern-info-value">{{ formModel.workHous }}小时</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用人单位卡片 -->
      <div class="modern-card" v-if="formModel?.employer">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="worker" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>用人单位信息</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-info-grid">
            <div class="modern-info-item">
              <label>单位名称</label>
              <div class="modern-info-value">{{ formModel.employer.name }}</div>
            </div>
            <div class="modern-info-item">
              <label>负责人</label>
              <div class="modern-info-value">{{ formModel.employer.fzr }}</div>
            </div>
            <div class="modern-info-item">
              <label>办公电话</label>
              <div class="modern-info-value">{{ formModel.employer.bgdh }}</div>
            </div>
            <div class="modern-info-item">
              <label>联系人</label>
              <div class="modern-info-value">{{ formModel.employer.xgh }}</div>
            </div>
            <div class="modern-info-item">
              <label>单位地址</label>
              <div class="modern-info-value">{{ formModel.employer.dwdz }}</div>
            </div>
            <div class="modern-info-item">
              <label>单位描述</label>
              <div class="modern-info-value">{{ formModel.employer.bz }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 岗位描述卡片 -->
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="log" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>岗位描述</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-feature-list">
            <!--          <p>{{ formModel.bz }}</p>-->
            <div class="modern-feature-text">{{ formModel.bz }}</div>
          </div>
        </div>
      </div>

      <!-- 岗位职责卡片 -->
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="bookmark" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>岗位职责</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-feature-list">
            <div class="modern-feature-text" v-for="(item, index) in splitLines(formModel.gwzz)" :key="index">
              {{ item }}
            </div>
          </div>
        </div>
      </div>

      <!-- 岗位要求卡片 -->
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="star" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>岗位要求</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-tag-container">
            <div class="modern-tag" v-for="(item, index) in splitLines(formModel.gwyq)" :key="index">
              <el-icon>
                <Check/>
              </el-icon>
              <span>{{ item }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 招聘条件卡片 -->
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="people" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>招聘条件</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-feature-list">
            <div class="modern-feature-text" v-for="(item, index) in splitLines(formModel.zptj)" :key="index">
              {{ item }}
            </div>
          </div>
        </div>
      </div>

      <!-- 面试信息卡片 -->
      <div class="modern-card modern-interview-info" v-if="formModel.sfms === '是'">
        <div class="modern-card-header">
          <div class="modern-card-icon">
            <IconPark name="calendar-three" size="18" strokeWidth="3" theme="filled"/>
          </div>
          <h3>面试信息</h3>
        </div>
        <div class="modern-card-body">
          <div class="modern-info-grid">
            <div class="modern-info-item" v-if="formModel.mssj">
              <label>面试时间</label>
              <div class="modern-info-value">{{ formModel.mssj }}</div>
            </div>
            <div class="modern-info-item" v-if="formModel.msdd">
              <label>面试地点</label>
              <div class="modern-info-value">{{ formModel.msdd }}</div>
            </div>
            <div class="modern-info-item" v-if="formModel.sfqd === '是' && formModel.dkfw">
              <label>打卡范围</label>
              <div class="modern-info-value">{{ formModel.dkfw }}米</div>
            </div>
            <div class="modern-info-item" v-if="formModel.interviewResult">
              <label>面试状态</label>
              <div class="modern-info-value">{{ formModel.interviewResult }}</div>
            </div>
            <div class="modern-info-item" v-if="formModel.interviewComment">
              <label>面试评价</label>
              <div class="modern-info-value">{{ formModel.interviewComment }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="modern-actions" v-if="enterType==='apply'">
      <el-button type="primary"
                 class="modern-apply-btn"
                 @click="handleApply">
        立即申请
        <el-icon class="modern-btn-icon">
          <ArrowRight/>
        </el-icon>
      </el-button>
    </div>

    <!-- 装饰元素 -->
    <div class="modern-decoration">
      <div class="modern-dot dot-1"></div>
      <div class="modern-dot dot-2"></div>
      <div class="modern-line line-1"></div>
    </div>
  </div>
  <apply-model v-model="showApplyModel" :jobId="formModel.id"/>
</template>

<script setup>
import {useRouter} from 'vue-router';
import {Check, ArrowRight} from '@element-plus/icons-vue';
import ApplyModel from '@/views/qgzx/qgzx-sutdent-apply/components/apply-model.vue';
import {ref, watchEffect} from 'vue';
import {formatWorkDays, getJobTypeName} from '../utils/index.js';
import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user.js';
import IconPark from '@/components/IconPark/index.vue';

const router = useRouter();
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

const props = defineProps({
  formItems: Array,
  formModel: Object,
  enterType: String,
});

const showApplyModel = ref(false);

const handleApply = () => {
  // 这里可以添加申请逻辑
  showApplyModel.value = true;
};

// 辅助函数：将文本按换行符分割成数组
const splitLines = (text) => {
  if (!text) return [];
  return text.split('\n').filter(line => line.trim() !== '');
};

// 创建一个响应式变量来存储岗位类型名称
const jobTypeName = ref('');

// 监听 formModel.jobTypeId 变化
watchEffect(async () => {
  if (props.formModel?.jobTypeId) {
    jobTypeName.value = await getJobTypeName(props.formModel.jobTypeId);
    console.log('formModel.jobTypeId', props.formModel.jobTypeId,jobTypeName.value);
  }
});
</script>

<style lang="less" scoped>
@import "../css/qgzx-job-detail.less";
</style>
