<template>
  <div class="modern-job-detail" style="padding: 0!important">
    <!-- 主要内容区域 -->
    <div class="modern-content">
      <div class="modern-card-body">
        <div class="modern-info-grid">
          <div class="modern-info-item">
            <label>学年学期</label>
            <div class="modern-info-value">{{ formModel.xnxq || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>申请时间</label>
            <div class="modern-info-value">{{ formModel.sqsj || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>申请岗位</label>
            <div class="modern-info-value">{{ formModel.xxmc || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>是否服从安排</label>
            <div class="modern-info-value">{{ formModel.sffcap || '-' }}</div>
          </div>
        </div>
        <div class="modern-info-row">
          <div class="modern-info-item">
            <label>申请理由</label>
            <div class="modern-info-value ">{{ formModel.sqly || '-' }}</div>
          </div>
        </div>
        <div class="modern-info-row">
          <div class="modern-info-item">
            <label>特长优势</label>
            <div class="modern-info-value ">{{ formModel.tcys || '-' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>

const props = defineProps({
  formModel: Object,
});
</script>

<style lang="less" scoped>
//@import '../css/approval-edit-detail.less';
@import "../css/qgzx-job-detail.less";
.modern-job-detail .modern-card-body {
  padding: 8px!important;
}
</style>
