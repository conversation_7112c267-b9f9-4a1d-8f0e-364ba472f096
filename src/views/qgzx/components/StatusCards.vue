<template>
  <div class="status-cards-container">
    <el-row :gutter="8">
      <el-col
        v-for="item in tabsItems"
        :key="item.label"
        :xs="24"
        :sm="12"
        :md="6"
        :lg="6"
      >
        <el-card :body-style="{ padding: '5px' }"
                 shadow="hover"
                 :class="['status-card', { 'active': activeTab === item.name }]"
                 @click="handleClick(item.name)"
        >
          <div class="card-content">
            <div class="status-info">
              <div class="status-name">{{ item.label }}</div>
              <div class="status-count">{{ item.count || 0 }}</div>
            </div>
            <div class="status-icon">
              <el-icon :size="28" :color="getStatusColor(item.name)">
                <component :is="getStatusIcon(item.name)"/>
              </el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {
  Clock,
  CircleCheck,
  CircleClose,
  Refresh,
} from '@element-plus/icons-vue';
import {getNodeState} from '@/views/qgzx/qgzx-job-approval/api/index.js';
import {ElMessage} from 'element-plus';
import {useRouter} from 'vue-router';
import {comApproveStatus, insertAtIndex} from '@/utils/common_bak2.js';

const props = defineProps({
  activeTab: {
    type: String,
    default: '待审批',
  },
  moduleCode: String,
});

const router = useRouter();
const {query} = router.currentRoute.value;
const emit = defineEmits(['tab-change']);

const activeName = ref(null);
const tabsItems = ref([]);
const nodeStateArray = ref([]);
const checkedNodeId = ref(query?.currentNodeId ?? null);

// 获取状态对应的图标
const getStatusIcon = (statusName) => {
  const iconMap = {
    '待审批': Clock,
    '通过': CircleCheck,
    '不通过': CircleClose,
    '退回': Refresh,
  };
  return iconMap[statusName] || Clock;
};

// 获取状态对应的颜色
const getStatusColor = (statusName) => {
  const colorMap = {
    '待审批': '#409EFF',
    '通过': '#67C23A',
    '不通过': '#F56C6C',
    '退回': '#E6A23C',
  };
  return colorMap[statusName] || '#409EFF';
};

// 查询节点状态
const queryNodeState = async () => {
  try {
    const data = await getNodeState({moduleCode: props.moduleCode});
    if (data) {
      nodeStateArray.value = data.map(item => ({
        ...item,
        value: item.nodeId,
      }));

      checkedNodeId.value = query?.currentNodeId || data[0]?.nodeId;
      const resultToUse = query?.currentResult;

      handleDialogSubmit(resultToUse);
    }
  } catch (e) {
    ElMessage.error(e.message);
  }
};

// 处理对话框提交
const handleDialogSubmit = (presetResult = null) => {
  if (!checkedNodeId.value) {
    ElMessage.error('请选择你要审核的节点信息');
    return;
  }

  const checkedNode = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
  let baseItems = comApproveStatus();

  baseItems.forEach(item => {
    item.label = checkedNode[item.diyname];
    item.name = checkedNode[item.prename];
    // 添加图标和颜色信息
    item.icon = getStatusIcon(item.name);
    item.color = getStatusColor(item.name);
  });

  insertAtIndex(baseItems, {
    label: '待审批',
    name: '待审批',
    icon: getStatusIcon('待审批'),
    color: getStatusColor('待审批'),
  }, 0);

  activeName.value = presetResult || baseItems[0].name;
  emit('tabActiveName', activeName.value);
  tabsItems.value = baseItems;
};

// 点击卡片切换状态
const handleClick = (tab) => {
  emit('tab-change', tab);
};

// 初始化加载数据
onMounted(() => {
  queryNodeState();
});

// 暴露刷新方法
defineExpose({
  refresh: queryNodeState,
});
</script>

<style scoped lang="scss">
.status-cards-container {
  .status-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0 !important;

    &.active {
      border-left: 4px solid transparent;
      border-left-color: var(--el-color-primary);
      background-color: rgba(64, 158, 255, 0.05);
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-info {
        .status-name {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 3px;
        }

        .status-count {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
        }
      }

      .status-icon {
        opacity: 0.8;
      }
    }
  }
}
</style>
