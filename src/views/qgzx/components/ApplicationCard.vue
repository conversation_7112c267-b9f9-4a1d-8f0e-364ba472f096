<template>
  <div class="tech-card">
    <!-- 顶部信息栏 -->
    <div class="card-header" @click="$emit('detail', data)">
      <div class="user-info">
        <template v-if="data?.userInfo">
          <el-avatar
            v-if="data.userInfo.photo"
            :size="60"
            class="user-avatar"
            :src="data.userInfo.xgh ? `/api/personInfo/${data.userInfo.xgh}/photo?access_token=${accessToken}` : ''">
            {{ data.userInfo?.xm?.charAt(0) || '' }}
          </el-avatar>
          <template v-else>
            <el-avatar
              v-if="data.userInfo.xb === '男'"
              :size="60"
              src="/male.png"
              :alt="data.userInfo.xb"
              class="user-avatar"/>
            <el-avatar
              v-else-if="data.userInfo.xb === '女'"
              :size="60"
              src="/female.png"
              :alt="data.userInfo.xb"
              class="user-avatar"/>
          </template>
        </template>
        <div class="user-details">
          <div class="student-info-container">
            <div class="student-name-row">
              <h3 class="student-name">
                <span>{{ data?.userInfo?.xm || '--' }}</span>
                <span class="student-meta">{{ data?.userInfo?.xgh || '--' }}</span>
              </h3>
              <div class="status-badge" :class="getStatusClass(data.spzt)">
                {{ data.spzt || '-' }}
              </div>
            </div>
            <div class="student-meta-row">
              <div class="apply-time detail-row">
                <IconPark name="history" size="15" strokeWidth="3"/>
                <span class="span-wrap">{{ formatDateTime(data.sqsj) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <job-card
      v-if="data.jobApplication"
      :data="data.jobApplication"
      @detail="emit('detail', data)"
      @attendance="emit('attendance', data)"
      @salary="emit('salary', data)"
      class="job-card"
      :enter-type="currentActiveName"
    />
  </div>
</template>

<script setup>
import {formatDateTime, getStatusClass} from '@/views/qgzx/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';
import {getToken} from '@/utils/token-util.js';
import JobCard from '@/views/qgzx/components/JobCard.vue';

defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({
      userInfo: {},
      jobApplication: null,
      spzt: '',
      sqsj: '',
    }),
  },
  currentActiveName: {
    type: String,
    default: 'keshenqing',
    validator: (val) => ['keshenqing', 'yishenqing'].includes(val),
  },
});

const accessToken = getToken();
const emit = defineEmits(['detail', 'interview', 'attendance', 'salary']);
</script>

<style lang="less" scoped>
@primary-color: #6366f1;
@text-primary: #333333; // 使用6位hex颜色
@text-secondary: #64748b;
@text-tertiary: #606266;
@border-color: fade(#000000, 5%); // 使用fade函数替代rgba
@shadow-light: 0 4px 20px fade(#000000, 8%);
@shadow-hover: 0 8px 25px fade(#000000, 12%);
@transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

/* 基础卡片样式 */
.tech-card {
  position: relative;
  border: 1px solid @border-color;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: @shadow-light;
  transition: @transition;
  margin: 4px 0;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: @shadow-hover;
    border-color: fade(@primary-color, 20%); // 使用fade函数

    .user-avatar {
      transform: scale(1.05);
    }
  }
}

/* 头部区域 */
.card-header {
  padding: 5px 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  transition: transform 0.3s ease;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.student-info-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 学生姓名行 */
.student-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.student-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: @text-primary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;

  .highlight {
    color: @primary-color;
  }
}

.student-meta {
  padding-left: 3px;
  font-size: 12px;
  color: @text-secondary;
  white-space: nowrap;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: #f5f7fa;
  color: @text-primary;
  white-space: nowrap;

  /* 状态颜色 */

  &.approved {
    background-color: #e6f7e6;
    color: #67c23a;
  }

  &.pending {
    background-color: #f0f7ff;
    color: @primary-color;
  }

  &.rejected {
    background-color: #fff0f0;
    color: #f56c6c;
  }
}

/* 元信息行 */
.student-meta-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}


.apply-time {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: @text-tertiary;

  .span-wrap {
    margin-left: 3px;
  }
}

/* 工作卡片 */
.job-card {
  margin: 8px;
  border-top: 1px solid @border-color;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tech-card {
    font-size: 13px;
  }

  .card-header {
    padding: 10px 12px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
  }

  .student-name {
    font-size: 15px;
  }

  .status-badge,
  .student-meta,
  .apply-time {
    font-size: 11px;
  }
}

/* QQ浏览器字体修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .tech-card {
    font-size: 14px !important;
    -webkit-text-size-adjust: 100% !important;
  }

  .student-name {
    font-size: 16px !important;
  }

  .status-badge,
  .student-meta,
  .apply-time {
    font-size: 12px !important;
  }
}
</style>
