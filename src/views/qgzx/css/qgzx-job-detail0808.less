/* 基础重置与变量设置 */
.modern-job-detail {
  * {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
  }

  position: relative;
  margin: 0 auto;
  padding: 4px;
  overflow-x: hidden;

  /* 针对QQ浏览器的字体大小修复 */
  font-size: 16px; /* 添加基础字体大小 */
  -webkit-text-size-adjust: 100%; /* 防止QQ浏览器自动调整文字大小 */
}

/* 颜色变量 */
@primary-color: #409EFF;
@success-color: #67c23a;
@danger-color: #f56c6c;
@info-color: #909399;
@text-color: #2c3e50;
@light-text: #555;
@border-color: fade(black, 5);  /* 5% 透明度 */
@shadow-color: fade(black, 5);  /* 5% 透明度 */

/* 卡片样式 */
.modern-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px @shadow-color;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid fade(black, 3);  /* 3% 透明度 */

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 16px fade(black, 10);  /* 10% 透明度 */
  }

  &-body {
    padding: 0.8rem;
  }

  &-header {
    display: flex;
    align-items: center;
    padding: 4px;
    border-bottom: 1px solid @border-color;
    background-color: fade(@primary-color, 3);  /* 3% 透明度 */

    h3 {
      margin: 0;
      font-size: 0.66rem;
      color: @text-color;
      font-weight: 600;
    }
  }

  &-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: fade(@primary-color, 10);  /* 10% 透明度 */
    color: @primary-color;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
  }
}

/* 状态徽章 */
.status-badge {
  margin-left: 12px;
  display: inline-block;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  font-size: 0.5rem;
  font-weight: 500;
  white-space: nowrap;
  color: #fff;
  text-align: center;
  transition: all 0.3s ease;

  /* 针对QQ浏览器的最小字号限制 */
  min-font-size: 12px; /* 非标准属性，部分浏览器支持 */
  -webkit-text-size-adjust: none; /* 防止自动调整 */

  &.pending {
    background: linear-gradient(135deg, @primary-color, #a3d4ff);
  }

  &.approved {
    background: linear-gradient(135deg, #4caf50, #7fda82);
  }

  &.rejected {
    background: linear-gradient(135deg, @danger-color, #f89b9b);
  }

  &.completed {
    background: linear-gradient(135deg, @info-color, #c0c4cc);
  }
}

/* 布局与网格 */
.modern-content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.modern-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 0.6rem;
}

/* 标签样式 */
.modern-tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
}

.modern-tag {
  display: flex;
  align-items: center;
  background-color: fade(@primary-color, 8);  /* 8% 透明度 */
  color: @primary-color;
  padding: 0.4rem;
  border-radius: 16px;
  font-size: 0.6rem;
  transition: all 0.3s ease;

  /* 针对QQ浏览器的字体大小修复 */
  min-font-size: 12px; /* 设置最小字体大小 */
  -webkit-text-size-adjust: none; /* 禁用自动调整 */

  .el-icon {
    font-size: 0.6rem;
    margin-right: 0.2rem;
  }

  &:hover {
    background-color: fade(@primary-color, 15);  /* 15% 透明度 */
    transform: translateY(-2px);
  }
}

/* 文本与内容 */
.modern-info-value {
  font-size: 0.6rem;
  color: @text-color;
  font-weight: 500;

  /* 针对QQ浏览器的修复 */
  min-font-size: 12px;
  -webkit-text-size-adjust: none;
}

/* 特征列表 */
.modern-feature-list {
  padding: 0.1rem;
  list-style: none;
  font-size: 0.6rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.4rem;

  .modern-feature-text {
    color: #34495e;
    font-size: 0.6rem;
    line-height: 1.4;

    /* 针对QQ浏览器的修复 */
    min-font-size: 12px;
    -webkit-text-size-adjust: none;
  }
}

/* 按钮与操作 */
.modern-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding: 1rem 0;
}

.modern-apply-btn {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px fade(@primary-color, 30);  /* 30% 透明度 */

  .modern-btn-icon {
    margin-left: 0.3rem;
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px fade(@primary-color, 40);  /* 40% 透明度 */

    .modern-btn-icon {
      transform: translateX(3px);
    }
  }
}

/* 工作详情特定样式 */
.description-container {
  margin-top: 6px;
}

.ellipsis-text {
  color: @light-text;
  font-size: 14px;
  line-height: 1.5;

  /* 针对QQ浏览器的修复 */
  -webkit-text-size-adjust: 100%;
}

.job-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: #34495e;

  /* 针对QQ浏览器的修复 */
  -webkit-text-size-adjust: 100%;
}

.job-details {
  margin-top: 8px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  font-size: 13px;
  color: @light-text;

  /* 针对QQ浏览器的修复 */
  -webkit-text-size-adjust: 100%;

  .span-wrap {
    padding-left: 6px;
    color: @light-text;
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;

    /* 针对QQ浏览器的修复 */
    -webkit-text-size-adjust: 100%;
  }
}

.time-grid {
  margin-top: 8px;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px 4px 2px;
  border-radius: 6px;

  /* 针对QQ浏览器的修复 */
  -webkit-text-size-adjust: 100%;
}

/* 操作区域 */
.action-area {
  cursor: pointer;
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed var(--el-border-color-light);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  margin: 0 4px;
  border-radius: 4px;
  color: #5a5e66;
  font-size: 12px;
  transition: all 0.3s;

  /* 针对QQ浏览器的修复 */
  -webkit-text-size-adjust: 100%;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px fade(black, 10);  /* 10% 透明度 */
    background-color: #edf2f7;
    color: @primary-color;
  }
}

/* QQ浏览器特定修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modern-job-detail .modern-card {
    box-shadow: 0 2px 10px fade(black, 8);  /* 8% 透明度 */
  }

  .modern-job-detail .modern-tag {
    transform: translateZ(0);
  }

  /* 针对QQ浏览器的字体大小修复 */
  body {
    -webkit-text-size-adjust: 100% !important;
  }

  .modern-job-detail {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100% !important;
  }

  .modern-card-header h3,
  .modern-info-value,
  .modern-tag,
  .status-badge,
  .modern-feature-text,
  .detail-row,
  .time-item,
  .action-btn {
    font-size: 12px !important; /* 设置最小可接受的字号 */
    -webkit-text-size-adjust: 100% !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-area {
    justify-content: center;
  }

  /* 移动端字体大小调整 */
  .modern-card-header h3 {
    font-size: 14px !important;
  }

  .modern-info-value,
  .modern-tag,
  .status-badge,
  .modern-feature-text {
    font-size: 12px !important;
  }
}
