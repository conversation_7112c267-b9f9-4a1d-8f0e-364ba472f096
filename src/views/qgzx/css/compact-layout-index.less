// compact-layout-index.less
// 紧凑布局公共样式

/* 紧凑头部布局 */
.compact-header {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 8px;
  padding: 6px 8px 0 8px;

  .header-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;

    .status-cards {
      flex: 1;
    }
  }
}

/* 内容容器 */
.content-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 增强的流体视图容器 */
.enhanced-fluid-viewport {
  display: flex;
  flex-direction: column;

  .loading-skeleton {
    margin-top: 10px;

    .skeleton-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(24%, 1fr));
      gap: 8px;

      .card-skeleton {
        height: 180px;
        border-radius: 12px;
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .empty-btn {
      margin-top: 20px;
      padding: 8px 24px;
      border-radius: 8px;
    }
  }
}

/* 增强的卡片容器 */
.enhanced-card-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 增强的卡片网格 */
.enhanced-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(32.5%, 1fr));
  gap: 6px;
  padding: 4px;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.4);
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(144, 147, 153, 0.6);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(144, 147, 153, 0.1);
    border-radius: 4px;
  }
}

/* 增强的卡片样式 */
.enhanced-card {
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --card-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.12);
  --card-shadow-active: 0 6px 16px rgba(0, 0, 0, 0.1);

  position: relative;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: var(--card-shadow);
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  overflow: hidden;
  z-index: 0;

  /* 卡片悬停效果 */
  &:hover {
    transform: translateY(-6px);
    box-shadow: var(--card-shadow-hover);
    z-index: 1;

    &::before {
      opacity: 1;
    }
  }

  /* 卡片点击效果 */
  &:active {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-active);
  }

  /* 卡片悬浮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c, #f56c6c);
    opacity: 0;
    transition: opacity 0.3s;
  }

  /* 卡片边框效果 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    pointer-events: none;
  }
}

/* 分页样式 */
.pagination-wrapper {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: center;
  background: #ffffff;
}
