/* 颜色变量 */
@primary-color: #409EFF;
@success-color: #67c23a;
@danger-color: #f56c6c;
@info-color: #909399;
@text-color: #2c3e50;
@light-text: #555;
@border-color: fade(#000, 5%);
@shadow-color: fade(#000, 5%);

/* 基础重置 */
.modern-job-detail {
  * {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }

  position: relative;
  margin: 0 auto;
  padding: 4px;
  overflow-x: hidden;
  font-size: 16px;
  -webkit-text-size-adjust: 100%;

  /* 卡片基础样式 */

  .modern-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px @shadow-color;
    border: 1px solid @border-color;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 16px fade(#000, 10%);
    }

    &-header {
      display: flex;
      align-items: center;
      padding: 3px 6px;
      border-bottom: 1px solid @border-color;
      background-color: fade(@primary-color, 3%);

      h3 {
        margin: 0;
        font-size: 16px;
        color: @text-color;
        font-weight: 600;
      }
    }

    &-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background-color: fade(@primary-color, 10%);
      color: @primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }

    &-body {
      padding: 16px;
    }
  }

  /* 内容区域 */

  .modern-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  /* 信息网格布局 */

  .modern-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 8px;

  }

  .modern-info-row {
    margin-bottom: 8px;
  }

  .modern-info-item {
    label {
      display: block;
      font-size: 14px;
      color: @light-text;
      margin-bottom: 4px;
    }

    .modern-info-value {
      font-size: 14px;
      color: @text-color;
      font-weight: 500;
      word-break: break-word;
    }
  }

  .modern-feature-text {
    font-size: 14px;
  }

  .span-wrap {
    padding-left: 6px;
  }
  .modern-tag {
    font-size: 14px;
  }

  /* 操作按钮 */

  .modern-actions {
    margin-top: 24px;
    text-align: center;

    .modern-apply-btn {
      border-radius: 20px;
      padding: 10px 24px;
      font-weight: 500;
      box-shadow: 0 3px 10px fade(@primary-color, 30%);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px fade(@primary-color, 40%);
      }

      .modern-btn-icon {
        margin-left: 6px;
        transition: transform 0.3s;
      }

      &:hover .modern-btn-icon {
        transform: translateX(3px);
      }
    }
  }
}

/* 状态徽章 */
.status-badge {
  margin-left: 12px;
  display: inline-block;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  font-size: 0.5rem;
  font-weight: 500;
  white-space: nowrap;
  color: #fff;
  text-align: center;
  transition: all 0.3s ease;

  /* 针对QQ浏览器的最小字号限制 */
  min-font-size: 12px; /* 非标准属性，部分浏览器支持 */
  -webkit-text-size-adjust: none; /* 防止自动调整 */

  &.pending {
    background: linear-gradient(135deg, @primary-color, #a3d4ff);
  }

  &.approved {
    background: linear-gradient(135deg, #4caf50, #7fda82);
  }

  &.rejected {
    background: linear-gradient(135deg, @danger-color, #f89b9b);
  }

  &.completed {
    background: linear-gradient(135deg, @info-color, #c0c4cc);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-job-detail {
    .modern-info-grid {
      grid-template-columns: 1fr;
    }

    .modern-card {
      &-header h3 {
        font-size: 15px;
      }
    }
  }
}

/* QQ浏览器专属修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modern-job-detail {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100% !important;

    .modern-card-header h3,
    .modern-info-value {
      font-size: 14px !important;
    }
  }
}
