/* 颜色变量 */
@primary-color: #409EFF;
@success-color: #67c23a;
@danger-color: #f56c6c;
@info-color: #909399;
@text-color: #2c3e50;
@light-text: #555;
@border-color: fade(#000, 5%);
@shadow-color: fade(#000, 5%);

/* 卡片基础样式 */
.modern-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px @shadow-color;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid @border-color;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 16px fade(#000, 10%);
  }

  .modern-card-header {
    display: flex;
    align-items: center;
    padding: 4px;

    h3 {
      margin: 0;
      font-size: 16px;
      color: @text-color;
      font-weight: 600;
    }
  }

  .modern-card-body {
    padding: 6px;
  }

  .modern-card-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: fade(@primary-color, 10%);
    color: @primary-color;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
  }
}

/* 网格布局 */
.modern-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 8px;

  .modern-info-item {
    label {
      display: block;
      font-size: 14px;
      color: @light-text;
      margin-bottom: 6px;
    }
  }
}

/* 信息展示 */
.modern-info-row {
  margin-bottom: 12px;
}

.modern-info-value {
  font-size: 14px;
  color: @text-color;
  font-weight: 500;
  line-height: 1.5;
}

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-align: center;
  transition: all 0.3s ease;

  &.pending {
    background: linear-gradient(135deg, @primary-color, #a3d4ff);
  }

  &.approved {
    background: linear-gradient(135deg, @success-color, #7fda82);
  }

  &.rejected {
    background: linear-gradient(135deg, @danger-color, #f89b9b);
  }

  &.completed {
    background: linear-gradient(135deg, @info-color, #c0c4cc);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-card {
    border-radius: 8px;

    .modern-card-header {
      padding: 10px 12px;

      h3 {
        font-size: 15px;
      }
    }

    .modern-card-body {
      padding: 12px;
    }
  }

  .modern-info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* QQ浏览器修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modern-card {
    .modern-card-header h3,
    .modern-info-value {
      font-size: 14px !important;
    }
  }

  .status-badge {
    font-size: 12px !important;
  }
}
