import request from '@/utils/request';

/**
 * 用人单位分页查询考勤记录（权限标识：workstudy:qgzxAttendanceRecord:listByEmployer）
 */
export async function queryRecordPageByEmployer(params) {
    const res = await request.get('/workstudy/qgzx-attendance-record/page-by-employer', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询考勤记录（权限标识：workstudy:qgzxAttendanceRecord:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryRecordPage(params) {
    const res = await request.get('/workstudy/qgzx-attendance-record/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 查询考勤记录列表（权限标识：workstudy:qgzxAttendanceRecord:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryRecordList(params) {
    const res = await request.get('/qgzx-attendance-record/list', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 统计学生工作时长（权限标识：workstudy:qgzxAttendanceRecord:calculateWorkHours）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryCalculateWorkHours(params) {
    const res = await request.get('/qgzx-attendance-record/calculate-work-hours', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 用人单位按学生分组查询考勤统计数据（权限标识：workstudy:qgzxAttendanceRecord:listByEmployer）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryAttendanceRecordPage(params) {
  const res = await request.get('/workstudy/qgzx-attendance-record/student-stat-page-by-employer', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
