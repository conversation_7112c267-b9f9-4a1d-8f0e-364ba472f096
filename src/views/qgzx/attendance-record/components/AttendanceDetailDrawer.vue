<template>
  <ele-drawer
    size="50%"
    v-model="drawerVisible"
    :title="`${data?.studentName || '学生'} - 考勤详情`"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-click-modal="false"
    class="attendance-detail-drawer"
  >
    <!-- 学生基本信息 -->
    <div class="student-info-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">学号：</span>
          <span class="info-value">{{ data?.userInfo.xgh || '--' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">联系电话：</span>
          <span class="info-value">{{ data?.userInfo.sjh || '--' }}</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">岗位名称：</span>
          <span class="info-value">{{ data?.jobName || '--' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">所属部门：</span>
          <span class="info-value">{{ data?.employerName || '--' }}</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">学年学期：</span>
          <span class="info-value">{{ data?.xnxq || '--' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">正常率：</span>
          <span class="info-value">{{ (data?.normalRate * 100).toFixed(2) || 0 }}%</span>
        </div>
      </div>
    </div>

    <!-- 考勤统计摘要 -->
    <div class="attendance-summary">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">总记录</div>
            <div class="stat-value">{{ data?.totalRecords || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card success">
            <div class="stat-title">正常</div>
            <div class="stat-value">{{ data?.normalCount || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card warning">
            <div class="stat-title">迟到/早退</div>
            <div class="stat-value">{{ (data?.lateCount || 0) + (data?.earlyLeaveCount || 0) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card danger">
            <div class="stat-title">缺勤</div>
            <div class="stat-value">{{ data?.absentCount || 0 }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 时间线展示考勤记录 -->
    <div class="timeline-section">
      <div class="section-header">
        <el-icon>
          <Clock/>
        </el-icon>
        <span>考勤记录</span>
      </div>
      <el-timeline v-if="attendanceRecords.length > 0">
        <el-timeline-item
          v-for="(record, index) in attendanceRecords"
          :key="index"
          :timestamp="formatDateTime(record.clockTime)"
          placement="top"
          :type="getTimelineType(record.attendanceStatus)"
          :color="getTimelineColor(record.attendanceStatus)"
        >
            <div class="record-card">

              <div class="record-item">
                <span class="record-value">{{ record.attendanceType }}</span>
                <div class="record-status" :class="getStatusClass(record.attendanceStatus)">
                  {{ record.attendanceStatus }}
                </div>
              </div>

              <div class="record-content">
                <div class="record-item" v-if="record.location">
                  <span class="record-label">打卡位置：</span>
                  <span class="record-value">{{ record.location }}</span>
                </div>
              </div>
            </div>
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else description="暂无考勤记录"/>
    </div>

  </ele-drawer>
</template>

<script setup>
import {ref, watch, computed} from 'vue';
import {Clock} from '@element-plus/icons-vue';
import {queryRecordPageByEmployer} from '../api/index.js';

const props = defineProps({
  modelValue: Boolean,
  data: Object,
  RandomString: String,
});

const emit = defineEmits(['update:modelValue']);

const drawerVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
});

// 考勤记录数据
const attendanceRecords = ref([]);

// 获取考勤记录
const fetchAttendanceRecords = async () => {
  if (!props.data?.studentApplyId) return;

  try {
    const res = await queryRecordPageByEmployer({
      studentApplyId: props.data.studentApplyId,
    });
    console.log(res)
    attendanceRecords.value = res.list || [];
  } catch (error) {
    console.error('获取考勤记录失败:', error);
    attendanceRecords.value = [];
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '--';
  const date = new Date(dateTimeString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }).replace(/\//g, '-');
};

// 获取时间线类型
const getTimelineType = (status) => {
  switch (status) {
    case '正常':
      return 'success';
    case '迟到':
    case '早退':
      return 'warning';
    case '请假':
    case '缺勤':
      return 'danger';
    default:
      return 'primary';
  }
};

// 获取时间线颜色
const getTimelineColor = (status) => {
  switch (status) {
    case '正常':
      return '#67C23A';
    case '迟到':
    case '早退':
      return '#E6A23C';
    case '请假':
    case '缺勤':
      return '#F56C6C';
    default:
      return '#409EFF';
  }
};

// 获取状态类名
const getStatusClass = (status) => {
  return {
    正常: 'status-normal',
    迟到: 'status-late',
    早退: 'status-early-leave',
    缺勤: 'status-absent',
    请假: 'status-leave',
  }[status] || '';
};

// 导出考勤记录
const exportAttendance = () => {
  // 这里实现导出逻辑
  console.log('导出考勤记录', props.data);
  ElMessage.success('导出功能待实现');
};

// 监听data变化
// watch(() => props.data, (newVal) => {
//   if (newVal) {
//     fetchAttendanceRecords();
//   }
// }, { immediate: true });

watch(
  () => props.RandomString,
  () => {
    fetchAttendanceRecords();
  }, {immediate: true});
</script>

<style lang="scss" scoped>
.attendance-detail-drawer {
  :deep(.el-drawer__body) {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .student-info-section {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;

    .info-row {
      display: flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;
      font-size: 14px;

      .info-label {
        color: #909399;
        margin-right: 5px;
      }

      .info-value {
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .attendance-summary {
    margin-bottom: 20px;

    .stat-card {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      text-align: center;
      border-left: 4px solid #409EFF;

      &.success {
        border-left-color: #67C23A;
      }

      &.warning {
        border-left-color: #E6A23C;
      }

      &.danger {
        border-left-color: #F56C6C;
      }

      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .timeline-section {
    flex: 1;
    overflow-y: auto;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .record-card {
      .record-status {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin: 8px;

        &.status-normal {
          background-color: #f0f9eb;
          color: #67C23A;
        }

        &.status-late, &.status-early-leave {
          background-color: #fdf6ec;
          color: #E6A23C;
        }

        &.status-absent {
          background-color: #fef0f0;
          color: #F56C6C;
        }

        &.status-leave {
          background-color: #ecf5ff;
          color: #409EFF;
        }
      }

      .record-content {
        .record-item {
          margin-bottom: 6px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          .record-label {
            color: #909399;
            margin-right: 5px;
          }

          .record-value {
            color: #606266;
          }
        }
      }
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #e8e8e8;
  }
}

:deep(.el-timeline) {
  padding-left: 10px;

  .el-timeline-item__timestamp {
    font-size: 13px;
    color: #909399;
    margin-bottom: 5px;
  }
}
</style>
