<template>
  <ele-drawer size="76%"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '8px!important', background:'#f0f2f5', height: (pageHeight+98)+'px', overflow:'auto'}"
              :modal-append-to-body="true"
              :append-to-body="true"
              :close-on-click-modal="false"
              class="modern-job-drawer"
              :show-close="false">
    <!-- 抽屉头部 - 科技感设计 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-content">
          <div class="header-left">
            <div class="header-title">{{ title }} {{ filteredList.length }}人
            </div>
            <div class="modern-header-line"></div>
          </div>
          <div class="header-right">
            <el-button type="danger" @click="updateModelValue(false)">
              <el-icon class="el-icon--left">
                <CircleCloseFilled/>
              </el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="modern-layout">
      <!-- 加载状态 - 科技感骨架屏 -->
      <div v-if="loading" class="loading-container">
        <div class="skeleton-grid">
          <div v-for="i in 6" :key="i" class="skeleton-card">
            <div class="skeleton-avatar shimmer"></div>
            <div class="skeleton-content">
              <div class="skeleton-line shimmer" style="width: 70%"></div>
              <div class="skeleton-line shimmer" style="width: 50%"></div>
              <div class="skeleton-line shimmer" style="width: 60%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div v-else-if="filteredList.length === 0" class="empty-container">
        <div class="empty-content">
          <div class="empty-illustration">
            <svg width="180" height="120" viewBox="0 0 180 120" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M30 30H150V90H30V30Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
              <circle cx="60" cy="60" r="12" fill="#E4E7ED"/>
              <rect x="80" y="52" width="32" height="16" rx="2" fill="#E4E7ED"/>
              <path d="M20 100H160V110H20V100Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
            </svg>
          </div>
          <div class="empty-text">暂无考勤数据</div>
          <el-button type="primary" @click="resetFilters" class="reload-btn">
            <span>重新加载</span>
          </el-button>
        </div>
      </div>

      <!-- 数据展示区域 -->
      <template v-else>
        <div class="card-grid">
          <div
            v-for="item in paginatedData"
            :key="item.studentApplyId"
            @click.stop="openDetail(item)"
            class="tech-card"
          >
            <!-- 学生信息卡片 -->
            <div class="student-card">
              <div class="student-avatar">
                <template v-if="item.userInfo">
                  <el-avatar
                    v-if="item.userInfo.photo"
                    :size="60"
                    class="avatar-bg"
                    :src="item.userInfo.xgh ? `/api/personInfo/${item.userInfo.xgh}/photo?access_token=${accessToken}` : ''">
                    {{ item.userInfo?.xm?.charAt(0) || '' }}
                  </el-avatar>
                  <template v-else>
                    <el-avatar
                      v-if="item.userInfo.xb === '男'"
                      :size="60"
                      src="/male.png"
                      :alt="item.userInfo.xb"
                      class="avatar-bg"/>
                    <el-avatar
                      v-else-if="item.userInfo.xb === '女'"
                      :size="60"
                      src="/female.png"
                      :alt="item.userInfo.xb"
                      class="avatar-bg"/>
                  </template>
                </template>

                <!--                <div class="avatar-bg" :style="{ background: getAvatarColor(item.xgh) }"></div>-->
                <!--                <div class="avatar-text">{{ getInitials(item.studentName) }}</div>-->
                <!--                <div class="avatar-pulse"></div>-->
              </div>
              <div class="student-info">
                <h3 class="student-name">
                  {{ item.userInfo.xm || '--' }}
                  <span class="student-id">{{ item.userInfo.xgh || '--' }}</span>
                </h3>
                <div class="contact-info">
                  <span>电话: {{ item.userInfo.sjh || '未提供' }}</span>
                </div>
                <div class="last-attendance">
                  <span>最后考勤: {{ item.lastAttendanceDate || '无记录' }}</span>
                </div>
              </div>
            </div>

            <!-- 考勤统计卡片 -->
            <div class="attendance-stats">
              <div class="stats-header">
                <div class="stats-title">考勤统计</div>
                <div class="normal-rate">
                  <div class="rate-progress">
                    <div class="progress-bg"></div>
                    <div
                      class="progress-fill"
                    ></div>

                    <!--                    :style="{ width: `${(item.normalRate * 100).toFixed(2)}%` }"-->
                    <div class="rate-text">{{ (item.normalRate * 100).toFixed(2) || 0 }}%</div>
                  </div>
                  <span class="rate-label">正常率</span>
                </div>
              </div>

              <div class="stats-grid">
                <!-- 总记录/总工时 -->
                <div class="stat-card total" @click="filterByType('total')">
                  <div class="stat-content">
                    <div class="stat-title">总记录/总工时</div>
                    <div class="stat-value">{{ item.totalRecords || 0 }}/{{ item.totalWorkHours || 0 }}<span
                      class="unit">h</span></div>
                  </div>
                </div>

                <!-- 正常记录 -->
                <div class="stat-card success" @click="filterByType('normal')">
                  <div class="stat-content">
                    <div class="stat-title">正常</div>
                    <div class="stat-value">{{ item.normalCount || 0 }}</div>
                  </div>
                </div>

                <!-- 迟到记录 -->
                <div class="stat-card warning" @click="filterByType('late')">
                  <div class="stat-content">
                    <div class="stat-title">迟到</div>
                    <div class="stat-value">{{ item.lateCount || 0 }}</div>
                  </div>
                </div>

                <!-- 早退记录 -->
                <div class="stat-card warning" @click="filterByType('early')">
                  <div class="stat-content">
                    <div class="stat-title">早退</div>
                    <div class="stat-value">{{ item.earlyLeaveCount || 0 }}</div>
                  </div>
                </div>

                <!-- 缺勤记录 -->
                <div class="stat-card danger" @click="filterByType('absent')">
                  <div class="stat-content">
                    <div class="stat-title">缺勤</div>
                    <div class="stat-value">{{ item.absentCount || 0 }}</div>
                  </div>
                </div>

                <!-- 请假记录 -->
                <div class="stat-card info" @click="filterByType('leave')">
                  <div class="stat-content">
                    <div class="stat-title">请假</div>
                    <div class="stat-value">{{ item.leaveCount || 0 }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div v-if="filteredList.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="filteredList.length"
            :page-sizes="[12, 24, 36, 48]"
            layout="total, sizes, prev, pager, next"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
            small
            background
            class="tech-pagination"
          />
        </div>
      </template>
    </div>

    <!-- 详情抽屉组件 -->
    <AttendanceDetailDrawer v-model="detailVisible" :data="detailData"
                            :RandomString="RandomString"/>
  </ele-drawer>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {ElMessage} from 'element-plus';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import AttendanceDetailDrawer from '@/views/qgzx/attendance-record/components/AttendanceDetailDrawer.vue';
import {queryAttendanceRecordPage} from '../api/index.js';
import {getToken} from '@/utils/token-util.js';
import PaperPerview from '@/views/exam/components/paper-preview.vue';
import {generateRandomString} from '@/utils/common_bak2.js';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '考勤管理',
  },
  jobId: {
    type: String,
  },
});

const emit = defineEmits(['update:modelValue', 'filter']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const accessToken = getToken();
const RandomString = ref(null);

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(12);
const searchQuery = ref('');

// 查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  jobId: props.jobId,
  search: searchQuery.value,
}));

// 过滤后的列表
const filteredList = computed(() => {
  return fluidData.value.filter(item => {
    return searchQuery.value === '' ||
      (item.studentName?.includes(searchQuery.value)) ||
      (item.xgh?.includes(searchQuery.value));
  });
});

// 分页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredList.value.slice(start, end);
});

// 获取考勤数据
const fetchData = async () => {
  try {
    loading.value = true;
    const res = await queryAttendanceRecordPage(queryParams.value);
    fluidData.value = res?.list || [];
  } catch (e) {
    ElMessage.error(e.message || '获取考勤数据失败');
  } finally {
    loading.value = false;
  }
};

// 重置筛选条件
const resetFilters = () => {
  searchQuery.value = '';
  currentPage.value = 1;
  fetchData();
};

// 分页变化处理
const handlePageChange = (page) => {
  currentPage.value = page;
  window.scrollTo({top: 0, behavior: 'smooth'});
};

// 每页数量变化处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 详情相关
const detailVisible = ref(false);
const detailData = ref(null);

// 打开详情
const openDetail = (item) => {
  RandomString.value = generateRandomString(10);
  detailData.value = item;
  detailVisible.value = true;
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

// 导出记录
const exportRecord = (item) => {
  ElMessage.success(`导出 ${item.studentName} 的考勤记录`);
  // 这里添加实际的导出逻辑
};

// 按类型筛选
const filterByType = (type) => {
  emit('filter', type);
};

// 监听props变化
watch(() => props.modelValue, (val) => {
  console.log(val);
  if (val && props.jobId) {
    fetchData();
  }
}, {immediate: true});

</script>

<style lang="scss" scoped>
/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--el-color-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-primary-rgb), 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

/*glow 动画是一个“发光”效果的定义*/
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(var(--el-color-primary-rgb), 0.3); /* 初始较弱光 */
  }
  50% {
    box-shadow: 0 0 15px rgba(var(--el-color-primary-rgb), 0.5); /* 高亮发光 */
  }
  100% {
    box-shadow: 0 0 5px rgba(var(--el-color-primary-rgb), 0.3); /* 回到初始光 */
  }
}

/* 全局样式变量 */
.modern-job-drawer {
  --primary-color: var(--el-color-primary);
  --success-color: var(--el-color-success);
  --warning-color: var(--el-color-warning);
  --danger-color: var(--el-color-danger);
  --info-color: var(--el-color-info);
  --text-primary: var(--el-text-color-primary);
  --text-secondary: var(--el-text-color-secondary);
  --bg-light: var(--el-bg-color-page);
  --border-light: var(--el-border-color-light);
  --card-shadow: var(--el-box-shadow-light);
  --card-hover-shadow: 0 8px 24px var(--el-color-primary);
  --glass-effect: rgba(255, 255, 255, 0.85);
  --transition-fast: all 0.2s ease;
  --transition-medium: all 0.3s ease;

  /* 抽屉头部样式 */
  :deep(.ele-drawer-header) {
    margin-bottom: 0;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--bg-light), var(--el-bg-color));
    border-bottom: 1px solid var(--border-light);
    backdrop-filter: blur(5px);
  }


  .drawer-header {
    width: 100%;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .header-left {
        flex: 1;
        position: relative;

        .header-title {
          font-size: 0.7rem;
          font-weight: 500;
          margin: 0 0 0.4rem 0;
          color: #2c3e50;
          position: relative;
        }

        .modern-header-line {
          height: 3px;
          width: 80px;
          background: linear-gradient(90deg, var(--el-color-primary), rgba(var(--el-color-primary-rgb), 0.3));
          border-radius: 3px;
        }
      }

      .header-right {
        margin-left: 20px;

        .close-btn {
          border-radius: 6px;
          transition: all 0.3s ease;
          font-weight: 500;
          border: 1px solid rgba(245, 108, 108, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
            background: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }

  /* 加载状态样式 */
  .loading-container {
    padding: 12px;

    .skeleton-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 12px;

      .skeleton-card {
        background: var(--glass-effect);
        border-radius: 8px;
        padding: 16px;
        box-shadow: var(--card-shadow);
        display: flex;
        gap: 12px;
        align-items: center;
        backdrop-filter: blur(5px);

        .skeleton-avatar {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background: #f0f2f5;
          position: relative;
          overflow: hidden;
        }

        .skeleton-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 6px;

          .skeleton-line {
            height: 10px;
            background: #f0f2f5;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
          }
        }

        .shimmer {
          background: linear-gradient(to right, #f0f2f5 8%, #e0e3e8 18%, #f0f2f5 33%);
          background-size: 800px 104px;
          animation: shimmer 1.5s infinite linear;
        }
      }
    }
  }

  /* 空状态样式 */
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: var(--glass-effect);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(5px);

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .empty-illustration {
        svg {
          path, circle, rect {
            transition: var(--transition-medium);
          }
        }
      }

      .empty-text {
        font-size: 14px;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .reload-btn {
        border-radius: 6px;
        padding: 6px 16px;
        font-size: 13px;
        transition: var(--transition-fast);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.2);
        }
      }
    }
  }

  /* 卡片网格布局 */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(32%, 1fr));
    gap: 8px;
    animation: fadeIn 0.3s ease-out;
  }

  /* 科技感卡片样式 */
  .tech-card {
    cursor: pointer;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 12px;
    transition: var(--transition-medium);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    animation: glow 3s infinite;

    &:hover {
      transform: translateY(-3px);
      box-shadow: var(--card-hover-shadow);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }

    /* 学生卡片样式 */
    .student-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding-bottom: 12px;
      border-bottom: 1px dashed rgba(0, 0, 0, 0.08);
      cursor: pointer;

      .student-avatar {
        position: relative;
        width: 48px;
        height: 48px;
        flex-shrink: 0;

        .avatar-bg {
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: 10px;
          background: var(--primary-color);
          opacity: 0.9;
          box-shadow: 0 3px 8px rgba(var(--el-color-primary-rgb), 0.3);
        }

        .avatar-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 14px;
          font-weight: 600;
          text-transform: uppercase;
        }

        .avatar-pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: 10px;
          background: rgba(var(--el-color-primary-rgb), 0.1);
          animation: pulse 2s infinite;
          pointer-events: none;
        }
      }

      .student-info {
        flex: 1;
        min-width: 0;

        .student-name {
          font-size: 14px;
          margin: 0 0 4px 0;
          color: var(--text-primary);
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          .student-id {
            font-size: 13px;
            color: var(--text-secondary);
            margin-left: 6px;
            font-weight: normal;
          }
        }

        .contact-info, .last-attendance {
          font-size: 13px;
          color: var(--text-secondary);
          margin-bottom: 2px;
        }
      }
    }

    /* 考勤统计区域 */
    .attendance-stats {
      .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .stats-title {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .normal-rate {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;

          .rate-progress {
            position: relative;
            width: 100px;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;

            .progress-bg {
              position: absolute;
              width: 100%;
              height: 100%;
              background: rgba(241, 245, 249, 0.8);
            }

            .progress-fill {
              position: absolute;
              height: 100%;
              background: linear-gradient(90deg, var(--success-color), #85ce61);
              border-radius: 3px;
              transition: width 0.6s ease;
            }

            .rate-text {
              position: absolute;
              right: -36px;
              top: 50%;
              transform: translateY(-50%);
              font-size: 13px;
              font-weight: 600;
              color: var(--success-color);
            }
          }

          .rate-label {
            font-size: 13px;
            color: var(--text-secondary);
          }
        }
      }

      /* 统计卡片网格 */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .stat-card {
          display: flex;
          align-items: center;
          padding: 8px;
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.7);
          box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
          transition: var(--transition-fast);
          position: relative;
          overflow: hidden;
          cursor: pointer;
          backdrop-filter: blur(2px);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          /* 不同类型卡片边框色 */
          &.total {
            border-left: 3px solid rgba(var(--el-color-primary-rgb), 0.7);
          }

          &.success {
            border-left: 3px solid var(--success-color);
          }

          &.warning {
            border-left: 3px solid var(--warning-color);
          }

          &.danger {
            border-left: 3px solid var(--danger-color);
          }

          &.info {
            border-left: 3px solid var(--info-color);
          }

          .stat-content {
            flex: 1;

            .stat-title {
              font-size: 13px;
              color: var(--text-secondary);
              margin-bottom: 2px;
            }

            .stat-value {
              font-size: 14px;
              font-weight: 700;
              color: var(--text-primary);

              .unit {
                font-size: 13px;
                color: var(--text-secondary);
              }
            }
          }
        }
      }
    }
  }

  /* 分页控件样式 */
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid var(--border-light);

    .tech-pagination {
      :deep(.btn-prev),
      :deep(.btn-next),
      :deep(.number) {
        border-radius: 6px;
        margin: 0 2px;
        font-size: 13px;
        transition: var(--transition-fast);
      }

      :deep(.number.active) {
        background: var(--primary-color);
        color: white;
        font-weight: 600;
      }

      :deep(.number:hover) {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.2);
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-job-drawer {
    :deep(.el-drawer) {
      width: 100% !important;
    }

    .card-grid {
      grid-template-columns: 1fr;
    }

    .tech-card {
      padding: 12px;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }
}
</style>
