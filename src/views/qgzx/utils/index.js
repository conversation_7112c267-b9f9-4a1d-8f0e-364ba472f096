// 格式化函数
import dayjs from 'dayjs';
import {nextTick, ref} from 'vue';
import {useDictData} from '@/utils/use-dict-data.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

export function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return '未设置工作时间';
  return `${dayjs(startDate).format('YYYY-MM-DD')} 至 ${dayjs(endDate).
    format('YYYY-MM-DD')}`;
}

export function formatDateTime(dateTime) {
  if (!dateTime) return '未知时间';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm');
}

export function getAddress(jobAddresses) {
  if (!jobAddresses) return '';
  return jobAddresses?.[0]?.name || '-';
}

export function getStatusClass(status) {
  if (!status) return '';
  const statusMap = {
    '待审批': 'pending',
    '审批中': 'pending',
    '通过': 'approved',
    '不通过': 'rejected',
    // '已完成': 'completed',
  };
  return statusMap[status] || '';
}

/**
 * 辅助函数：格式化工作日显示
 * @param workDays
 * @returns {string}
 */
export function formatWorkDays(workDays) {
  if (!workDays) return '未设置工作日';
  const dayMap = {
    '1': '周一',
    '2': '周二',
    '3': '周三',
    '4': '周四',
    '5': '周五',
    '6': '周六',
    '7': '周日',
  };
  return (typeof workDays === 'string' ? workDays.split(',') : workDays).map(
    day => dayMap[day]).join('、');
}

// 辅助函数：获取岗位类型名称（这里需要根据实际数据结构调整）
// 创建一个缓存 ref 来存储 jobType 数据
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
const jobTypeCache = ref(null);

export async function getJobTypeName(typeId) {
  // 如果缓存中没有数据，先获取数据
  if (!dicts.value['jobTypeData'] && !jobTypeCache.value) {
    try {
      console.log('开始获取岗位类型数据...');
      // 等待字典数据加载完成
      await useDictData(['jobTypeData'], {
        dictFieldUrl: '/workstudy/qgzx-job-type',
      });
      // 确保数据已更新到 store
      await nextTick();
      // 设置缓存
      jobTypeCache.value = dicts.value['jobTypeData'];
      console.log('数据获取完成，缓存设置:', jobTypeCache.value);

    } catch (error) {
      console.error('获取岗位类型数据失败:', error);
      return typeId; // 返回原始ID作为回退
    }
  }
  // 使用缓存数据或store中的数据
  const dictsData = jobTypeCache.value || dicts.value['jobTypeData'] || [];
  // console.log('当前使用的数据:', dictsData);
  const existData = dictsData.filter(obj => obj.dictDataCode === typeId);
  // console.log('匹配结果:',
  //   existData.length > 0 ? existData[0].dictDataName : typeId);
  return existData.length > 0 ? existData[0].dictDataName : typeId;
}

