<template>
  <ele-page hide-footer flex-table>
    <!-- 顶部整合区域 - 紧凑布局 -->
    <div class="compact-header">
      <!-- 状态卡片和搜索组合 -->
      <div class="header-row">
        <status-cards
          :active-tab="activeName"
          @tab-change="handleTabChange"
          @tabActiveName="handleTabActiveName"
          class="status-cards"
          moduleCode="qgzx_gwsb"
        />
      </div>
      <search
        @search="reload"
        @reset="resetSearch"
        @change-display-mode="displayMode = $event"
        @open-edit="openEdit"
        @handle-delete="handleDelete"
        :display-mode="displayMode"
        :selections="selections"
      />
    </div>

    <!-- 内容区域 -->
    <div class="content-container"
         :style="{height: (pageHeight-80)+'px', overflow:'auto',padding: '0 5px'}">
      <!-- 表格模式 -->
      <ele-pro-table
        v-if="displayMode === 'table'"
        flex-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        class="compact-table"
      >
        <template #jobName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>

      <!-- 流体卡片模式 - 增强视觉效果 -->
      <div v-else class="enhanced-fluid-viewport">
        <div v-if="loading" class="loading-skeleton">
          <div class="skeleton-grid">
            <el-skeleton
              v-for="i in 8"
              :key="i"
              animated
              class="card-skeleton"
            />
          </div>
        </div>

        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-state">
            <el-empty description="暂无数据"/>
          </div>

          <div v-else class="enhanced-card-container">
            <!-- 增强的卡片网格 -->
            <div class="enhanced-card-grid">
              <job-card
                v-for="item in fluidData"
                :key="item.id"
                :data="item"
                @detail="openEdit"
                class="enhanced-card"
                enter-type="approval"
              />
            </div>

            <!-- 分页 -->
            <div v-if="fluidData.length > 0" class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="total"
                :page-sizes="[12, 24, 36, 48]"
                layout="total, sizes, prev, pager, next"
                @current-change="handlePageChange"
                @size-change="handleSizeChange"
                small
                background
              />
            </div>
          </div>
        </template>
      </div>
    </div>

    <edit :modelValue="showEdit" :data="editData" :currentActiveName="activeName" @done="reloadData"
          @update:modelValue="handleEditDrawerClose"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, watch, unref} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox, ElNotification} from 'element-plus';
import _ from 'lodash';
// API
import {queryQgzxJobApprovalPage, removes} from './api/index.js';
// 组件
import StatusCards from '../components/StatusCards.vue';
import Search from './components/Search.vue';
import JobCard from '@/views/qgzx/components/JobCard.vue';
// 工具函数
import {comColumns} from './utils/index.js';
import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user.js';
import Edit from './components/edit.vue';
import {usePageTab} from '@/utils/use-page-tab.js';

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

// 显示模式
const displayMode = ref('fluid');

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(12); // 调整为12的倍数便于4列布局
// 编辑相关
const showEdit = ref(false);
const editData = ref(null);

// 表格相关
const tableRef = ref(null);
const selections = ref([]);
const columns = computed(() => [
  ...comColumns(),
]);

// 状态标签
const activeName = ref(null);
let backActiveName = query?.currentActiveName ?? '待审批';
activeName.value = backActiveName;
// 详情相关
const showDetail = ref(false);
const currentItem = ref(null);

// 计算查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  spzt: activeName.value,
}));

// 搜索相关
const lastWhere = ref({});

// 初始化加载
onMounted(() => {
  fetchData();
});

const handleEditDrawerClose = (value) => {
  showEdit.value = value;
};

const handleTabActiveName = (tabActiveName) => {
  console.log(tabActiveName);
  activeName.value = tabActiveName;
};

// 切换标签
const handleTabChange = (tab) => {
  activeName.value = tab;
  currentPage.value = 1;
  reloadData();
};

// 重新加载数据
const reload = (where) => {
  lastWhere.value = where || {};
  currentPage.value = 1;
  reloadData();
};

// 重置搜索
const resetSearch = () => {
  lastWhere.value = {};
  reloadData();
};

// 获取数据
const fetchData = _.debounce(async () => {
  if (!activeName.value) return;

  try {
    loading.value = true;
    const params = {
      ...queryParams.value,
      ...lastWhere.value,
    };

    const res = await queryQgzxJobApprovalPage(params);
    fluidData.value = res.list || [];
    total.value = res.count || 0;
  } catch (e) {
    ElMessage.error(e.message);
  } finally {
    loading.value = false;
  }
}, 300);

// 重新加载数据
const reloadData = () => {
  if (displayMode.value === 'fluid') {
    fetchData();
  } else {
    tableRef.value?.reload?.();
  }
};

// 打开编辑
const openEdit = (row) => {
  console.log(row);
  editData.value = row ? {...row} : null;
  showEdit.value = true;
};

// 单个删除
const handleDelete = async (row) => {
  try {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      ElNotification({
        title: '系统提示',
        message: '请至少选择一条数据',
        type: 'warning',
        duration: 2500,
        position: 'top-right',
      });
      return;
    }
    await ElMessageBox.confirm(`确定要删除选中的 ${rows.length} 条记录吗?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await removes(rows.map((d) => d.id));
    ElMessage.success('删除成功');
    reloadData();
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.message || '删除失败');
    }
  }
};

// 分页变化
const handlePageChange = () => {
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 表格数据源
const datasource = ({page, limit, where, orders, filters}) => {
  if (!activeName.value) {
    return Promise.resolve({
      data: [],
      total: 0,
    });
  }

  return queryQgzxJobApprovalPage({
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    spzt: activeName.value,
  });
};

// 监听显示模式变化
watch(displayMode, (newVal) => {
  currentPage.value = 1;
  reloadData();
});

// 监听状态变化
watch(activeName, (newVal) => {
  currentPage.value = 1;
  reloadData();
});
</script>

<style scoped lang="less">
@import "../css/compact-layout-index.less";
</style>
