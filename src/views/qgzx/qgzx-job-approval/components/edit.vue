<template>
  <ele-drawer size="70%"
              title="岗位申报审核"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0 16px 16px', display: 'flex', flexDirection: 'column', height: 'calc(100% - 55px)' }"
              @update:modelValue="updateModelValue"
              class="modern-approval-drawer">
    <el-row>
      <el-col :md="detailVisible ? 12 : 24" :sm="12" :xs="24">
        <!-- 岗位基本信息卡片 -->
        <ele-card shadow="hover" class="modern-card"
                  :body-style="{padding: '8px 8px 2px !important'}">
          <template #header>
            <div class="modern-card-header">
              <h3>
                {{ formModel.jobName || '岗位信息' }}</h3>
            </div>
          </template>
          <template #extra>
            <el-button
              type="primary"
              size="small"
              link
              @click="toggleJobDetail"
              class="detail-btn"
            >
              {{ detailVisible ? '隐藏岗位详情' : '查看岗位详情' }}
              <el-icon>
                <ArrowRight/>
              </el-icon>
            </el-button>
          </template>

          <div class="modern-job-detail">
            <!-- 主要内容区域 -->
            <div class="modern-content">
              <div class="modern-card-body">
                <div class="modern-info-grid">
                  <div class="modern-info-item">
                    <label>岗位类别</label>
                    <div class="modern-info-value">{{ jobTypeName }}</div>
                  </div>
                  <div class="modern-info-item">
                    <label>工作时间</label>
                    <div class="modern-info-value">{{ formModel.startTime }} - {{ formModel.endTime }} ({{
                        formatWorkDays(formModel.workDays)
                      }})
                    </div>
                  </div>
                  <div class="modern-info-item">
                    <label>工作周期</label>
                    <div class="modern-info-value">{{ formModel.startDate }} 至 {{ formModel.endDate }}</div>
                  </div>
                  <div class="modern-info-item">
                    <label>工作地点</label>
                    <div class="modern-info-value">{{ formModel.xqmc }} {{ formModel.Addresses || '图书馆' }}</div>
                  </div>
                  <div class="modern-info-item">
                    <label>薪资待遇</label>
                    <div class="modern-info-value">{{ formModel.hourlyRate }}元/小时 (最高{{
                        formModel.yzgbc
                      }}元/月)
                    </div>
                  </div>
                  <div class="modern-info-item">
                    <label>用工人数</label>
                    <div class="modern-info-value">{{ formModel.ygrs }}人</div>
                  </div>
                  <div class="modern-info-item">
                    <label>联系方式</label>
                    <div class="modern-info-value">{{ formModel.lxfs }}</div>
                  </div>
                  <div class="modern-info-item">
                    <label>每月工时</label>
                    <div class="modern-info-value">{{ formModel.workHous }}小时</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </ele-card>
        <!-- 审核表单区域 -->
        <ele-card v-if="currentActiveName==='待审批'" class="modern-card" shadow="hover"
                  :body-style="{padding: '5px 0 2px 0!important'}">
          <template #header>
            <div class="modern-card-header">
              <h3> 审核信息</h3>
            </div>
          </template>
          <form-preview
            key="formRef"
            labelPosition="top"
            routeType="QgzxJobApproval"
            :currentGroup="approvalData"
            @onDoneGroup="onDoneGroup"
          />
        </ele-card>
        <!-- 审核记录时间线 -->
        <ele-card class="modern-card" shadow="hover">
          <template #header>
            <div class="modern-card-header">
              <h3>审核记录</h3>
            </div>
          </template>
          <ApprovalTimeline :activities="activities"/>
        </ele-card>
      </el-col>
      <el-col v-if="detailVisible" :md="12" :sm="12" :xs="24">
        <JobDetailViews :form-model="formModel"
                        enter-type="Views"
                        class="drawer-content"/>
      </el-col>
    </el-row>

    <!-- 底部操作按钮 -->
    <template #footer v-if="currentActiveName==='待审批'">
      <div class="drawer-footer">
        <el-button size="small" @click="updateModelValue(false)" class="cancel-btn">
          取消
        </el-button>
        <el-button size="small"
                   type="primary"
                   :loading="loading"
                   @click="save"
                   class="submit-btn">
          提交审核
        </el-button>
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch, watchEffect} from 'vue';
import {
  ElMessage,
  ElButton,
  ElIcon,
} from 'element-plus';
import {
  ArrowRight,
} from '@element-plus/icons-vue';
import {operation, getNodeState, getQqgzxJobApprovalNodesBySqId} from '../api/index.js';
import {comColumns} from '../utils/index.js';
import {comApproveStatus, generateForm, toFormDataWj} from '@/utils/common_bak2.js';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import ApprovalTimeline from '@/components/ApprovalTimeline/index.vue';
import {statusColor} from '@/utils/status-color/index.js';
import {getJobTypeName} from '@/views/qgzx/utils/index.js';
import JobDetailViews from '@/views/qgzx/components/JobDetailViews.vue';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
  currentActiveName: String,
});

// 状态管理
const loading = ref(false);
const formItems = ref([]);
const formModel = ref({});
const approvalData = ref(null);
const approvalStatus = ref(null);
const formRef = ref(null);
const proFormGroup = ref([]);
// 控制子抽屉显示
const detailVisible = ref(false);
const activities = ref([]);

// 切换岗位详情显示/隐藏
const toggleJobDetail = () => {
  detailVisible.value = !detailVisible.value;
};

// 保存审核
const save = async () => {
  try {
    if (!proFormGroup.value?.length) {
      ElMessage.warning('表单数据未加载完成');
      return;
    }

    const validationResults = await Promise.all(
      proFormGroup.value.map(group => group.validate?.() ?? Promise.resolve(true)),
    );

    if (!validationResults.every(Boolean)) {
      ElMessage.error('请检查表单填写是否正确');
      return;
    }

    const resData = proFormGroup.value[0]?.values || {};
    if (!resData.result) {
      ElMessage.error('请选择审核状态');
      return;
    }

    loading.value = true;
    const formData = toFormDataWj({...resData, applicationId: props.data.id});

    await operation(formData);
    ElMessage.success('操作成功');
    updateModelValue(false);
    emit('done');
  } catch (error) {
    ElMessage.error(error.message || '操作失败');
  } finally {
    loading.value = false;
  }
};

// 辅助函数：格式化工作日显示
const formatWorkDays = (days) => {
  if (!days || days.length === 0) return '';
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  return days.map(day => dayNames[parseInt(day) - 1]).join('、');
};

const updateModelValue = (value) => {
  detailVisible.value = false;
  proFormGroup.value = [];
  emit('update:modelValue', value);
};

const onDoneGroup = (data) => {
  const existingGroup = proFormGroup.value.find(obj => obj.groupId === data.groupId);
  if (existingGroup) {
    existingGroup.values = data.values;
  } else {
    proFormGroup.value.push(data);
  }
};

// 查询节点状态
const queryNodeState = async () => {
  try {
    const data = await getNodeState({
      moduleCode: 'qgzx_gwsb',
    });

    const baseItems = comApproveStatus();
    baseItems.forEach(item => {
      item.label = data[0][item.diyname];
      item.name = data[0][item.prename];
    });
    approvalStatus.value = baseItems;
  } catch (e) {
    ElMessage.error(e.message);
  }
};

const queryQqgzxJobApprovalNodesBySqId = async () => {
  try {
    const data = await getQqgzxJobApprovalNodesBySqId(props.data.id);
    activities.value = data.map(item => {
      const statusObj = statusColor().find(obj => obj.label === item.approvalNode.result) || {};
      return {
        ...item,
        title: item.approvalNode.nodeName,
        name: item.approvalNode.result,
        timestamp: item.approvalNode.updateTime,
        size: 'large',
        ...statusObj,
        icon: item.reviewType === '会签' ? 'Stamp' : statusObj.icon || 'CircleCloseFilled',
      };
    });
  } catch (e) {
    ElMessage.error(e.message);
  }
};

// 初始化表单
watch(() => props.modelValue, async (modelValue) => {
  if (modelValue) {
    // 重置表单
    formRef.value?.resetForm();
    await Promise.all([
      queryNodeState(),
      queryQqgzxJobApprovalNodesBySqId(),
    ]);
    const baseColumns = comColumns() || [];
    formModel.value = generateForm(baseColumns.filter(item => item.prop && !item.disabled));

    if (props.data) {
      approvalData.value = {
        groupName: '审核信息',
        title: '',
        id: props.data.id,
        sqId: props.data.id,
        nodeId: props.data.nodeId,
        infoType: 'QgzxJobApproval',
        workflowId: props.data.workflowId,
        listFlag: '否',
      };

      formModel.value = {
        ...formModel.value,
        ...props.data,
        workDays: typeof props.data?.workDays === 'string'
          ? props.data.workDays.split(',')
          : Array.isArray(props.data?.workDays)
            ? props.data.workDays
            : [],
        Addresses: props.data?.jobAddresses?.[0]?.name || '-',
      };
    }
  }
}, {immediate: true});

// 创建一个响应式变量来存储岗位类型名称
const jobTypeName = ref('');

// 监听 jobTypeId 变化
watchEffect(async () => {
  if (props.data?.jobTypeId) {
    jobTypeName.value = await getJobTypeName(props.data?.jobTypeId);
    console.log('formModel.jobTypeId', props.data.jobTypeId, jobTypeName.value);
  }
});
</script>

<style lang="less" scoped>
@import '../../css/approval-edit-detail';
</style>
