<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
      </template>
      <template #extra>
        <div class="view-switch">
          <el-radio-group v-model="displayMode" size="small">
            <el-radio-button label="fluid">
              <el-icon>
                <Menu/>
              </el-icon>
              卡片视图
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon>
                <Grid/>
              </el-icon>
              表格视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <!-- 表格模式 -->
      <ele-pro-table v-if="displayMode === 'table'"
                     ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }">
        <template #jobName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
      <!-- 流体卡片模式 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated/>
        </div>
        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无数据"/>
          </div>
          <el-row v-else :gutter="10">
            <el-col
              v-for="item in fluidData"
              :key="item.id"
              :lg="6"
              :md="8"
              :sm="12"
              :xs="24"
            >
              <ele-card
                shadow="hover"
                :body-style="{ padding: '0 5px!important',marginTop: '8px',cursor: 'pointer',overflow: 'hidden' }"
              >
                <div class="article-list" @click="openEdit(item)">
                  <div :key="item.id" class="list-item">
                    <div class="list-item-body">
                      <div style="flex: 1">
                        <ele-text type="primary" size="md">
                          <IconPark name="app-store" size="18" strokeWidth="3"/>
                          {{ item.jobName }}
                        </ele-text>
                        <div style="margin-top: 3px; display: flex; align-items: center">
                          <el-tag
                            v-for="tag in item.tags"
                            :key="tag"
                            type="info"
                            size="small"
                            :disable-transitions="true"
                            style="margin-right: 8px"
                          >
                            {{ tag }}
                          </el-tag>
                        </div>
                        <div style="margin-top: 10px">{{ item.bz }}</div>
                        <div style="margin-top: 10px; display: flex; align-items: center">
                          <!--                          <el-avatar :src="item.avatar" :size="24"/>-->
                          <ele-text type="secondary" style="flex: 1; ">
                            {{ item.employer?.name }} 申请于 {{ item.createTime }}
                          </ele-text>
                        </div>
                        <div style="margin-top: 10px; display: flex; align-items: center">
                          <ele-tooltip content="每月工作小时数" placement="bottom" effect="light">
                            <ele-text
                              :icon="Timer"
                              type="placeholder"
                              style="cursor: pointer"
                            >
                              &nbsp;{{ item.workHous }}
                            </ele-text>
                          </ele-tooltip>
                          <el-divider direction="vertical" style="margin: 0 14px"/>
                          <ele-tooltip content="时薪" placement="bottom" effect="light">
                            <ele-text
                              :icon="Wallet"
                              type="placeholder"
                              style="cursor: pointer"
                            >
                              &nbsp;{{ item.hourlyRate }}
                            </ele-text>
                          </ele-tooltip>
                          <el-divider direction="vertical" style="margin: 0 14px"/>
                          <ele-tooltip content="月最高报酬" placement="bottom" effect="light">
                            <ele-text
                              :icon="PriceTag"
                              type="placeholder"
                              style="cursor: pointer"
                            >
                              &nbsp;{{ item.yzgbc }}
                            </ele-text>
                          </ele-tooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ele-card>
            </el-col>
          </el-row>
          <div v-if="fluidData.length >0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>

    <edit v-model="showEdit" :data="editData" @done="reloadData"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, watch, unref} from 'vue';
import {Grid, Menu, PriceTag, Timer} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import Edit from './components/edit.vue';
import {comColumns} from './utils/index.js';
import {comApproveStatus, insertAtIndex} from '@/utils/common_bak2.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {Wallet} from '@icon-park/vue-next';
import _ from 'lodash';
import IconPark from '@/components/IconPark/index.vue';
import {usePageTab} from '@/utils/use-page-tab.js';
import {queryPageByEmployer} from '@/views/qgzx/remuneration-apply/api/index.js';

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
// 路由参数解析
const routeParams = computed(() => ({
  activeName: query?.activeName ?? '',
}));
const pathArray = path.split('/');
const currentJobId = pathArray[3];

console.log(routeParams.value, currentJobId);

const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/qgzx/qgzx-job-apply',
    query: {currentActiveName: routeParams.value.activeName},
  });
};

// 显示模式
const displayMode = ref('fluid'); // 'table' 或 'fluid'

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 表格相关
const tableRef = ref(null);
const selections = ref([]);
const columns = ref(comColumns());

// 标签页相关

// 编辑相关
const showEdit = ref(false);
const editData = ref(null);

const bodyStyle = computed(() => {
  return {
    overflow: 'hidden',
    ...(displayMode.value === 'fluid' ? {background: '#f0f2f5'} : {}),
    ...(displayMode.value === 'fluid' ? {padding: '0 0 10px !important'} : {padding: '0 10px 10px !important'}),
  };
});

// 计算属性 - 获取当前查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  jobId: currentJobId,
}));


// 搜索相关
const lastWhere = ref({});

// 初始化加载
onMounted(() => {
  fetchData();
});

// 获取数据（添加防抖）
const fetchData = _.debounce(async () => {
  if (!currentJobId) return;
  try {
    loading.value = true;

    const params = {
      ...queryParams.value,
      ...lastWhere.value,
    };

    const res = await queryPageByEmployer(params);

    if (displayMode.value === 'fluid') {
      fluidData.value = res.list || [];
    }

    total.value = res.count || 0;

  } catch (e) {
    ElMessage.error(e.message);
  } finally {
    loading.value = false;
  }
}, 300);

// 切换显示模式时重新加载数据
watch(displayMode, (newVal) => {
  currentPage.value = 1;
  if (currentJobId) {
    if (newVal === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
});

// 监听activeName变化
watch(currentJobId, (newVal) => {
  if (newVal) {
    currentPage.value = 1;
    if (displayMode.value === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
}, {immediate: false});

// 分页变化
const handlePageChange = () => {
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 打开编辑
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

// 重新加载数据
const reloadData = () => {
  currentPage.value = 1;
  if (displayMode.value === 'fluid') {
    fetchData();
  } else {
    tableRef.value?.reload?.();
  }
};

// 表格数据源
const datasource = ({page, limit, where, orders, filters}) => {
  if (!currentJobId) {
    return Promise.resolve({
      data: [],
      total: 0,
    });
  }

  return queryPageByEmployer({
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    jobId: currentJobId,
  });
};

</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid var(--el-border-color-light);
}

.list-item-body {
  display: flex;
  padding: 10px;
}

@media screen and (max-width: 576px) {
  .list-item-body {
    display: block;
  }
}
</style>
