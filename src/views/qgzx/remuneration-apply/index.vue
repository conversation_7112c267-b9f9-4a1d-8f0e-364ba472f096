<template>
  <div class="remuneration-page">
    <!-- 固定顶部操作栏 -->
    <div class="fixed-header">
      <!-- 顶部操作栏 -->
      <div class="top-bar">
        <!-- 返回按钮 -->
        <div class="back-btn" @click="onBack">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <span class="back-text">返回</span>
        </div>

        <!-- 查询表单 -->
        <div class="search-form">
          <el-form :inline="true" :model="queryParams" size="small">
            <el-form-item label="申报月份">
              <el-date-picker
                v-model="queryParams.sbny"
                type="month"
                value-format="YYYY-MM"
                placeholder="选择月份"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="content-wrapper">
      <!-- 合并统计与图表区域 -->
      <div class="combined-container">
        <el-row :gutter="8">
          <!-- 第一行统计卡片 -->
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-label">申报总数</div>
              <div class="stat-value">{{ stats.total || 0 }}</div>
              <div class="stat-compare">
                <span :class="stats.compare.total >= 0 ? 'up' : 'down'">
                  <el-icon :class="stats.compare.total >= 0 ? 'arrow-up' : 'arrow-down'"/>
                  {{ Math.abs(stats.compare.total) }}%
                </span>
                同比上月
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-label">发放总额</div>
              <div class="stat-value">¥{{ stats.amount || 0 }}</div>
              <div class="stat-compare">
                <span :class="stats.compare.amount >= 0 ? 'up' : 'down'">
                  <el-icon :class="stats.compare.amount >= 0 ? 'arrow-up' : 'arrow-down'"/>
                  {{ Math.abs(stats.compare.amount) }}%
                </span>
                同比上月
              </div>
            </div>
          </el-col>
          <!-- 第二行统计卡片 -->
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-label">平均工时</div>
              <div class="stat-value">{{ stats.avgHours || 0 }}h</div>
              <div class="stat-compare">
                <span :class="stats.compare.hours >= 0 ? 'up' : 'down'">
                  <el-icon :class="stats.compare.hours >= 0 ? 'arrow-up' : 'arrow-down'"/>
                  {{ Math.abs(stats.compare.hours) }}%
                </span>
                同比上月
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-label">通过率</div>
              <div class="stat-value">{{ stats.approvalRate || 0 }}%</div>
              <div class="stat-compare">
                <span :class="stats.compare.rate >= 0 ? 'up' : 'down'">
                  <el-icon :class="stats.compare.rate >= 0 ? 'arrow-up' : 'arrow-down'"/>
                  {{ Math.abs(stats.compare.rate) }}%
                </span>
                同比上月
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 月度申报趋势图表 -->
        <div class="trend-chart-wrapper">
          <div class="chart-title">月度申报趋势</div>
          <div ref="trendChart" class="chart-content"></div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <ele-pro-table
          ref="tableRef"
          row-key="id"
          :columns="columns"
          :datasource="tableData"
          :border="true"
          :show-overflow-tooltip="true"
          v-model:selections="selections"
          tooltip-effect="light"
          highlight-current-row>
          <template #toolbar>
            <div class="table-actions">
              <el-button type="primary" size="small" @click="handleGenerate">
                <span>一键生成</span>
              </el-button>
              <el-button type="success" size="small" @click="handleApply">
                <span>申报</span>
              </el-button>
            </div>
          </template>
          <!-- 申报月份列自定义显示 -->
          <template #sbny="{ row }">
            <ele-tooltip content="详情" placement="left" effect="light">
              <el-link type="primary" underline="never" @click="handleRowClick(row)">
                {{ row.sbny }}
              </el-link>
            </ele-tooltip>
          </template>
        </ele-pro-table>
      </div>
    </div>

    <!-- 详情抽屉组件 -->
    <RemunerationDetailDrawer
      :modelValue="detailVisible"
      :remunerationApplyId="currentDetail?.id"
      :title="currentDetail?.sbny+' 考勤明细'"
      @update:modelValue="handleDetailDrawerClose"/>

    <generate-model v-model="generateModelVisible" :data="currentDetail"/>
    <edit :modelValue="applyVisible" />
  </div>
</template>

<script setup>
import {ref, onMounted, nextTick, onBeforeUnmount, computed, unref} from 'vue';
import * as echarts from 'echarts';
import {queryPageByEmployer} from '@/views/qgzx/remuneration-apply/api/index.js';
import {ElMessage} from 'element-plus';
import {comColumns} from '@/views/qgzx/remuneration-apply/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';
import {usePageTab} from '@/utils/use-page-tab.js';
import {useRouter} from 'vue-router';
import RemunerationDetailDrawer from '@/views/qgzx/remuneration-apply/detail/RemunerationDetailDrawer.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import GenerateModel from '@/views/qgzx/remuneration-apply/components/generate-model.vue';
import edit from '@/views/qgzx/remuneration-apply/components/edit.vue';

// 路由相关
const {removePageTab, getRouteTabKey} = usePageTab();
/** 列表选中数据 */
const selections = ref([]);
// 表格列配置
const columns = ref(comColumns());

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
// 路由参数解析
const routeParams = computed(() => ({
  activeName: query?.activeName ?? '',
}));
const pathArray = path.split('/');
const currentJobId = pathArray[3];

console.log(routeParams.value, currentJobId);
// 查询参数
const queryParams = ref({
  sbny: '',
});
const generateModelVisible = ref(false);
const handleGenerate = () => {
  generateModelVisible.value = true;
};

const applyVisible = ref(false);
const handleApply = () => {
  applyVisible.value = true;
};

// 表格数据
const tableData = ref([]);
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
});

// 统计数据
const stats = ref({
  total: 0,
  amount: 0,
  avgHours: 0,
  approvalRate: 0,
  compare: {
    total: 0,
    amount: 0,
    hours: 0,
    rate: 0,
  },
});

// 详情相关
const currentDetail = ref(null);
const detailVisible = ref(false);

// 图表DOM引用
const trendChart = ref(null);

// 返回上一页
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/qgzx/qgzx-job-apply',
    query: {currentActiveName: routeParams.value.activeName},
  });
};

// 加载数据
const loadData = async () => {
  try {
    const params = {
      ...queryParams.value,
      pageNum: pagination.value.current,
      pageSize: pagination.value.size,
      jobId: currentJobId,
    };
    const res = await queryPageByEmployer(params);
    tableData.value = res.list || [];
    pagination.value.total = res.total || 0;

    // 处理统计信息
    processStats(res);

    // 渲染图表
    await nextTick(() => {
      renderTrendChart();
    });
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('数据加载失败');
  }
};

// 处理统计信息
const processStats = (data) => {
  const list = data.list || [];
  stats.value.total = list.length;
  stats.value.amount = list.reduce((sum, item) => sum + (item.totalAmount || 0), 0);
  stats.value.avgHours = list.length > 0
    ? (list.reduce((sum, item) => sum + (item.totalHours || 0), 0) / list.length).toFixed(1)
    : 0;
  stats.value.approvalRate = list.length > 0
    ? Math.round((list.filter(item => item.spzt === '通过').length / list.length) * 100)
    : 0;

  // 模拟同比数据
  stats.value.compare = {
    total: 12.5,
    amount: 8.2,
    hours: -3.4,
    rate: 2.1,
  };
};

// 渲染趋势图
const renderTrendChart = () => {
  const chart = echarts.init(trendChart.value);
  const option = {
    tooltip: {trigger: 'axis'},
    legend: {data: ['申报数量', '发放金额'], bottom: 0},
    xAxis: {
      type: 'category',
      data: ['4月', '5月', '6月', '7月'],
    },
    yAxis: [
      {type: 'value', name: '数量(件)'},
      {type: 'value', name: '金额(元)'},
    ],
    series: [
      {
        name: '申报数量',
        type: 'bar',
        data: [15, 23, 19, tableData.value.length],
      },
      {
        name: '发放金额',
        type: 'line',
        yAxisIndex: 1,
        data: [4200, 6500, 5800, stats.value.amount],
      },
    ],
  };
  chart.setOption(option);
};

// 查询处理
const handleSearch = () => {
  pagination.value.current = 1;
  loadData();
};

// 重置查询
const resetSearch = () => {
  queryParams.value = {
    xgh: '',
    sbny: '',
    spzt: '',
  };
  handleSearch();
};

// 行点击处理
const handleRowClick = (row) => {
  currentDetail.value = row;
  detailVisible.value = true;
};

// 处理详情抽屉关闭
const handleDetailDrawerClose = (value) => {
  detailVisible.value = value;
};

// 页面初始化
onMounted(() => {
  loadData();

  // 监听窗口大小变化，重新渲染图表
  window.addEventListener('resize', () => {
    if (trendChart.value) {
      echarts.getInstanceByDom(trendChart.value)?.resize();
    }
  });
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
/* 页面容器 */
.remuneration-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止外层容器滚动 */
  background-color: #f0f2f5;
}

/* 固定顶部区域 */
.fixed-header {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 8px 8px 1px 8px;
  background-color: #f0f2f5;
  flex-shrink: 0; /* 防止顶部栏被压缩 */
}

/* 内容区域 - 关键修改 */
.content-wrapper {
  flex: 1;
  overflow-y: auto; /* 只允许内容区域滚动 */
  padding: 0 8px 8px;
  //margin-bottom: 8px;
}

/* 顶部操作栏 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}


/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
  transition: color 0.3s;
}

.back-btn:hover {
  color: #409eff;
}

.back-text {
  margin-left: 6px;
  font-size: 14px;
}

/* 查询表单 */
.search-form {
  margin-top: 10px;
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

/* 合并统计与图表容器 */
.combined-container {
  margin: 8px 0;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 统计卡片样式 */
.stat-card {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.stat-compare {
  font-size: 12px;
  color: #909399;
}

/* 上升/下降样式 */
.up {
  color: #67c23a;
}

.down {
  color: #f56c6c;
}

/* 趋势图表包装器 */
.trend-chart-wrapper {
  margin-top: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #303133;
  text-align: center;
}

.chart-content {
  height: 260px;
}

/* 表格容器 */
.table-container {
  margin-top: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 表格操作按钮 */
.table-actions {
  display: flex;
  gap: 8px;
}
</style>
