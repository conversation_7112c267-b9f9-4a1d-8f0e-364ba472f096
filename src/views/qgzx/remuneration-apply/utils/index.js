export function comColumns() {
  return [
    // Selection column for table operations
    // {
    //   type: 'selection',
    //   columnKey: 'selection',
    //   width: 45,
    //   align: 'center',
    //   fixed: 'left',
    // },
    {
      prop: 'sbny',
      slot: 'sbny',
      label: '申报月份',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      colProps: 6,
      align: 'center',
    },
    {
      prop: 'xxmc',
      label: '申报项目',
      type: 'input',
      minWidth: 150,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      align: 'center',
      // formatter: (row) => row.employer?.name || '',
    },
    {
      prop: 'totalHours',
      label: '总工时(h)',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      align: 'center',
      colProps: 6,
    },
    {
      prop: 'totalAmount',
      label: '金额(元)',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      align: 'center',
    },
    {
      prop: 'spzt',
      label: '状态',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 12,
      align: 'center',
    },
    {
      prop: 'sqsj',
      label: '申报时间',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      align: 'center',
    },
  ];
}
