<template>
  <ele-drawer size="56%"
              title="薪酬申报"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '10px!important'}"
              @update:modelValue="updateModelValue">
    <TablePreview :ref="el => getRiskSpreadRef(el, 0)"
                  approverId="QgzxRemuneration"
                  dataName=""
                  approverType="QgzxRemuneration"
                  @onDoneGroup="handleDoneGroup"/>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {operation} from '../api/index.js';
import {comColumns} from '../utils/index.js';
import {generateForm} from '@/utils/common_bak2.js';
import CommonElForm from '@/components/CommonElForm/index.vue';
import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
import dayjs from 'dayjs';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
});

const isUpdate = ref(false);
const loading = ref(false);
const formRef = ref(null);
const formItems = ref([]);
const formModel = ref({
  declarationMonth: '', // 新增申报月份字段
});

const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};

const formData = ref(null);
const handleDoneGroup = async (data) => {
  formData.value = data;
};

// 保存逻辑
const save = async () => {
  try {
    // 验证申报月份是否已选择
    if (!formModel.value.declarationMonth) {
      EleMessage.warning('请选择申报月份');
      return;
    }

    // 验证申报月份是否为当前月或之前
    const currentMonth = dayjs().format('YYYY-MM');
    if (formModel.value.declarationMonth > currentMonth) {
      EleMessage.warning('只能选择当前月及之前的月份');
      return;
    }

    const isValid = await formRef.value?.validate();
    if (!isValid) {
      return false;
    }

    // 处理特殊字段格式
    const submitData = {
      ...formModel.value,
      workDays: formModel.value.workDays.join(','),
      declarationMonth: formModel.value.declarationMonth, // 包含申报月份
    };

    loading.value = true;
    operation(submitData).then(msg => {
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch(e => EleMessage.error(e.message)).finally(() => loading.value = false);
  } catch (e) {
    console.error('保存出错', e);
    EleMessage.error('保存失败，请重试');
  }
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

// 初始化表单
watch(() => props.modelValue, (modelValue) => {
  if (modelValue) {
    const baseColumns = comColumns() || [];
    formItems.value = baseColumns.filter(item => item.prop && !item.disabled);
    formModel.value = {
      declarationMonth: dayjs().format('YYYY-MM'), // 默认设置为当前月
    };

    if (props.data) {
      const hasJobAddresses = Array.isArray(props.data?.jobAddresses) && props.data.jobAddresses.length > 0;
      formModel.value = {
        ...formModel.value,
        ...generateForm(formItems.value),
        ...props.data,
        workDays: props.data?.workDays?.split(',') || [],
        Addresses: hasJobAddresses ? props.data?.jobAddresses[0].name : '',
        declarationMonth: props.data.declarationMonth || dayjs().format('YYYY-MM'), // 保留原有或设置默认
      };
      isUpdate.value = true;
    } else {
      formModel.value = {
        ...formModel.value,
        ...generateForm(formItems.value),
      };
      isUpdate.value = false;
    }
  } else {
    formRef.value?.clearValidate?.();
  }
}, {immediate: true});
</script>

<style scoped>
.declaration-month-container {
  padding: 0 20px 15px 20px;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
}
</style>
