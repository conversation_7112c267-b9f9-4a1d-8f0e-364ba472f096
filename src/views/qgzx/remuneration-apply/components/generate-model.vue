<!-- 岗位申请弹窗 -->
<template>
  <ele-modal :width="520"
             title="一键生成"
             :form="true"
             :model-value="modelValue"
             @update:modelValue="updateModelValue"
             :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
             :append-to-body="false"> <!-- 关键点：设为false -->


    <TablePreview :ref="el => getRiskSpreadRef(el, 0)"
                  approverId="QgzxRemuneration"
                  dataName="薪酬申报"
                  approverType="QgzxRemuneration"
                  @onDoneGroup="handleDoneGroup"/>
    <!--    <form-preview key="formRef"-->
    <!--                  labelPosition="top"-->
    <!--                  routeType="QgzxJobApply"-->
    <!--                  :currentGroup="approvalData"-->
    <!--                  @onDoneGroup="onDoneGroup"/>-->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        提交申请
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import {ref, unref, watch} from 'vue';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import {ElMessage as EleMessage} from 'element-plus';
import {toFormDataWj} from '@/utils/common_bak2.js';
import {operation} from '@/views/qgzx/qgzx-sutdent-apply/api/index.js';
import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
import BankData from '@/views/exam/test-paper/bank-data/index.vue';

const emit = defineEmits(['done', 'update:modelValue']);
const props = defineProps({
  /** 是否打开弹窗 */
  modelValue: Boolean,
  jobId: String,
});

const approvalData = ref(null);
const formRef = ref(null);
/** 导入请求状态 */
const loading = ref(false);


const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};


const formData = ref(null);
const handleDoneGroup = async (data) => {
  console.log(data)
  formData.value = data;
};



// 保存逻辑
const save = async () => {
  try {
    // 确保所有表单组都已加载
    if (!proFormGroup.value || proFormGroup.value.length === 0) {
      EleMessage.warning('表单数据未加载完成');
      return;
    }

    // 验证所有表单组
    const validationResults = await Promise.all(
      proFormGroup.value.map(async (group) => {
        if (group.validate) {
          return await group.validate();
        }
        return true; // 没有验证方法的组默认通过
      }),
    );

    if (!validationResults.every(Boolean)) {
      EleMessage.error('请检查表单填写是否正确');
      return;
    }

    // 业务逻辑验证
    let resData = proFormGroup.value.length > 0 ? proFormGroup.value[0].values : {};
    console.log(resData);
    // let newObj = toFormDataWj({...resData, jobId: props.jobId});
    // 提交数据...
    operation({...resData, jobId: props.jobId}).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  } catch (error) {
    console.error('保存过程中出错:', error);
    EleMessage.error('保存失败，请稍后重试');
  }
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

// 初始化表单
watch(() => props.modelValue, (modelValue) => {
  if (modelValue) {

    approvalData.value = {
      groupName: '申请信息',
      title: '',
      infoType: 'QgzxJobApply',
      listFlag: '否',
    };

  } else {
    formRef.value?.clearValidate?.();
  }
}, {immediate: true});
</script>

<style scoped>
/* 如果ele-modal支持custom-class */
:deep(.ele-modal-responsive > .el-overlay-dialog > .el-dialog) {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
</style>
