import request from '@/utils/request';

/**
 * 查询报酬明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
 */
export async function queryRemunerationDetail(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-detail',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询报酬明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
 * * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxRemunerationDetailPage(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-detail/page',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询报酬明细详情（权限标识：workstudy:qgzxRemunerationDetail:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function queryRemunerationDetailById(id) {
  const res = await request.get('/workstudy/qgzx-remuneration-detail/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申报ID查询明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
 * @param remunerationApplyId
 * @returns {Promise<*>}
 */
export async function queryQgzxRemunerationDetailByApplyId(remunerationApplyId) {
  const res = await request.get('/workstudy/qgzx-remuneration-detail/by-apply/' + remunerationApplyId);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 重新计算申报总计（权限标识：workstudy:qgzxRemunerationDetail:edit）
 * @param remunerationApplyId
 * @returns {Promise<*>}
 */
export async function queryQgzxRemunerationDetailRecalculate(remunerationApplyId) {
  const res = await request.get('/workstudy/qgzx-remuneration-detail/recalculate/' + remunerationApplyId);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 单个审核操作（权限标识：workstudy:qgzxRemunerationApply:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-detail/operation',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-detail/remove',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
