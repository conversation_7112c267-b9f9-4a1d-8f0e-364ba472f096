import request from '@/utils/request';

/**
 * 审核人查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
 */
export async function queryQgzxRemunerationApply(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-apply',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 审核人分页查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
 * * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxRemunerationApplyPage(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-apply/page',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询报酬申报（权限标识：workstudy:qgzxRemunerationApply:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function queryQgzxRemunerationApplyById(id) {
  const res = await request.get('/workstudy/qgzx-remuneration-apply/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 单个审核操作（权限标识：workstudy:qgzxRemunerationApply:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-apply/operation',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量审核操作（权限标识：workstudy:qgzxRemunerationApply:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function batchOperation(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-apply/batchOperation',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 基于考勤记录自动生成并保存报酬申报（权限标识：workstudy:qgzxRemunerationApply:apply）
 * @param data
 * @returns {Promise<*>}
 */
export async function generateAndSave(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-apply/generateAndSave',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 用人单位发起报酬申报
 * * @param data
 * @returns {Promise<*>}
 */
export async function applyByEmployer(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-apply/applyByEmployer',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 审核人批量删除报酬申报（权限标识：workstudy:qgzxRemunerationApply:remove）
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-remuneration-apply/remove',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 用人单位查询本单位的报酬申报
 * @param params
 * @returns {Promise<*>}
 */
export async function queryPageByEmployer(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-apply/pageByEmployer',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据岗位ID和申报年月查询申报记录（权限标识：workstudy:qgzxRemunerationApply:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryByJobAndMonth(params) {
  const res = await request.get('/workstudy/qgzx-remuneration-apply/by-job-and-month',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
