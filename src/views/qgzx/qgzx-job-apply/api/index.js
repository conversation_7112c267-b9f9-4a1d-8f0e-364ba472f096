import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryQgzxJobApply(params) {
  const res = await request.get('/workstudy/qgzx-job-apply', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxJobApplyPage(params) {
  const res = await request.get('/workstudy/qgzx-job-apply/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxJobApplyById(id) {
  const res = await request.get('/workstudy/qgzx-job-apply/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post(
    '/workstudy/qgzx-job-apply/operation-with-addresses', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operationApproval(data) {
  const res = await request.post(
    '/workstudy/qgzx-job-approval/edit-with-addresses', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-job-apply/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
