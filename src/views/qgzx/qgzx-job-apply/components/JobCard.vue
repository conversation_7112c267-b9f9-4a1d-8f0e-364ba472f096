<template>
  <el-card
    shadow="hover"
    class="job-card"
    :body-style="{ padding: '16px' }"
  >
    <div class="card-header">
      <div class="job-title">
        <el-tag
          :type="statusTagType"
          effect="light"
          size="small"
          class="status-tag"
        >
          {{ data.spzt }}
        </el-tag>
        <el-text
          tag="b"
          truncated
          class="title-text"
        >
          {{ data.jobName }}
        </el-text>
      </div>
      <div class="job-meta">
        <el-text type="info" size="small">
          {{ data.createTime }}
        </el-text>
      </div>
    </div>

    <div class="card-body">
      <div class="job-description">
        <el-text truncated :line-clamp="2">
          {{ data.bz || '暂无描述' }}
        </el-text>
      </div>

      <div class="job-tags">
        <el-tag
          v-for="tag in data.tags"
          :key="tag"
          type="info"
          size="small"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
      </div>

      <div class="job-details">
        <div class="detail-item">
          <el-icon><Location /></el-icon>
          <el-text truncated>
            {{ data.xqmc || '未知校区' }}
          </el-text>
        </div>

        <div class="detail-item">
          <el-icon><Calendar /></el-icon>
          <el-text truncated>
            {{ formatDateRange(data.startDate, data.endDate) }}
          </el-text>
        </div>

        <div class="detail-item">
          <el-icon><Clock /></el-icon>
          <el-text truncated>
            {{ data.startTime }}~{{ data.endTime }}
          </el-text>
        </div>

        <div class="detail-item">
          <el-icon><List /></el-icon>
          <el-text truncated>
            {{ formatWorkDays(data.workDays) }}
          </el-text>
        </div>
      </div>

      <div class="job-stats">
        <div class="stat-item">
          <el-tooltip content="每月工作小时数" placement="top">
            <div class="stat-content">
              <el-icon><Timer /></el-icon>
              <el-text>{{ data.workHous }}h</el-text>
            </div>
          </el-tooltip>
        </div>

        <div class="stat-item">
          <el-tooltip content="时薪" placement="top">
            <div class="stat-content">
              <el-icon><Wallet /></el-icon>
              <el-text>¥{{ data.hourlyRate }}</el-text>
            </div>
          </el-tooltip>
        </div>

        <div class="stat-item">
          <el-tooltip content="月最高报酬" placement="top">
            <div class="stat-content">
              <el-icon><PriceTag /></el-icon>
              <el-text>¥{{ data.yzgbc }}</el-text>
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <div class="employer-info">
        <el-avatar :size="24" :src="data.employer?.avatar" />
        <el-text truncated class="employer-name">
          {{ data.employer?.name || '未知申请人' }}
        </el-text>
      </div>

      <div class="action-buttons">
        <template v-if="data.spzt === '待审批'">
          <el-button
            size="small"
            type="primary"
            plain
            @click.stop="$emit('edit', data)"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="danger"
            plain
            @click.stop="$emit('delete', data)"
          >
            删除
          </el-button>
        </template>

        <template v-else-if="data.spzt === '通过'">
          <el-button
            v-if="data.sfms === '是'"
            size="small"
            type="success"
            plain
            @click.stop="$emit('interview', data)"
          >
            面试
          </el-button>
          <el-button
            size="small"
            type="warning"
            plain
            @click.stop="$emit('attendance', data)"
          >
            考勤
          </el-button>
          <el-button
            size="small"
            type="primary"
            plain
            @click.stop="$emit('salary', data)"
          >
            薪酬
          </el-button>
        </template>

        <template v-else>
          <el-button
            size="small"
            type="primary"
            plain
            @click.stop="$emit('edit', data)"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="danger"
            plain
            @click.stop="$emit('delete', data)"
          >
            删除
          </el-button>
        </template>

        <el-button
          size="small"
          plain
          @click.stop="$emit('view', data)"
        >
          详情
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  Location,
  Calendar,
  Clock,
  List,
  Timer,
  Wallet,
  PriceTag
} from '@element-plus/icons-vue'
import { statusColor} from '@/utils/status-color';
import { formatDateRange, formatWorkDays } from '../../utils/index.js'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const statusTagType = computed(() => statusColor(props.data.spzt))
</script>

<style scoped lang="scss">
.job-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .job-title {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      .status-tag {
        flex-shrink: 0;
        margin-right: 8px;
      }

      .title-text {
        font-size: 16px;
        flex: 1;
        min-width: 0;
      }
    }

    .job-meta {
      flex-shrink: 0;
      margin-left: 8px;
    }
  }

  .card-body {
    .job-description {
      margin-bottom: 12px;
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }

    .job-tags {
      margin-bottom: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .tag-item {
        margin-right: 0;
      }
    }

    .job-details {
      margin-bottom: 16px;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
        color: var(--el-text-color-secondary);

        .el-icon {
          margin-right: 8px;
          color: var(--el-text-color-placeholder);
          flex-shrink: 0;
        }

        .el-text {
          flex: 1;
          min-width: 0;
        }
      }
    }

    .job-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px 0;
      border-top: 1px dashed var(--el-border-color-light);
      border-bottom: 1px dashed var(--el-border-color-light);

      .stat-item {
        flex: 1;
        text-align: center;

        .stat-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .el-icon {
            margin-bottom: 4px;
            font-size: 18px;
            color: var(--el-color-primary);
          }

          .el-text {
            font-size: 14px;
          }
        }
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .employer-info {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;

      .el-avatar {
        margin-right: 8px;
      }

      .employer-name {
        flex: 1;
        min-width: 0;
        font-size: 13px;
      }
    }

    .action-buttons {
      flex-shrink: 0;
      margin-left: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
  }
}
</style>
