<template>
  <!-- 登录页面主容器，只有当系统信息加载完成时才显示 -->
  <div v-if="isSystemInfoLoaded" class="login-wrapper" :style="cssVars">
    <!-- 科技感背景元素 -->
    <div class="tech-bg">
      <div class="tech-grid"></div>
      <div class="tech-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
    </div>

    <!-- 登录内容容器 -->
    <div class="login-container">
      <!-- 登录卡片 -->
      <div class="login-card">
        <!-- 登录头部区域 -->
        <div class="login-header">
          <div class="logo-container">
            <div class="title-container">
              <!-- 动态校徽显示 -->
              <img
                v-if="systemInfo['图标+学校名称']?.attachment"
                :src="getImageUrl(systemInfo['图标+学校名称'].attachment)"
                alt="校徽"
                class="logo"
              >
              <!-- 默认校名显示 -->
              <template v-else>
                <h1 class="login-title">智慧大学</h1>
              </template>
              <div class="login-subtitle">智慧校园 · 安全登录</div>
            </div>
          </div>
        </div>
        <br/>

        <!-- 登录主体内容 -->
        <div class="login-body">
          <!-- 登录方式标签页 -->
          <div class="login-tabs">
            <!-- 统一身份认证标签 -->
            <!--            <div class="login-tab" :class="{active: activeTab === 'unified'}" @click="switchTab('unified')">-->
            <!--              <el-icon>-->
            <!--                <Connection/>-->
            <!--              </el-icon>-->
            <!--              <span>统一身份认证</span>-->
            <!--              <div class="tab-indicator"></div>-->
            <!--            </div>-->
            <!-- 账号密码登录标签 -->
            <div class="login-tab" :class="{active: activeTab === 'account'}" @click="switchTab('account')">
              <el-icon>
                <Key/>
              </el-icon>
              <span>账号密码登录</span>
              <div class="tab-indicator"></div>
            </div>
          </div>

          <!-- 统一身份认证内容区域 -->
          <div v-show="activeTab === 'unified'" class="unified-auth-content">
            <div class="auth-icon">
              <el-icon>
                <Connection/>
              </el-icon>
            </div>
            <h3>统一身份认证登录</h3>
            <p>您将跳转到学校统一身份认证平台进行登录</p>
            <el-button class="auth-button" @click="redirectToUnifiedAuth">
              前往认证平台
            </el-button>
            <div class="auth-footer">
              <el-link :underline="false" @click="switchTab('account')">
                使用账号密码登录 →
              </el-link>
            </div>
          </div>

          <!-- 账号密码登录表单区域 -->
          <div v-show="activeTab === 'account'" class="account-login-form">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              @keyup.enter="handleLogin"
              size="large"
              @submit.prevent
            >
              <!-- 用户名输入框 -->
              <el-form-item prop="username">
                <el-input
                  v-model="form.username"
                  placeholder="请输入学号/工号"
                  :prefix-icon="User"
                  clearable
                />
              </el-form-item>

              <!-- 密码输入框 -->
              <el-form-item prop="password">
                <el-input
                  v-model="form.password"
                  placeholder="请输入密码"
                  :prefix-icon="Lock"
                  show-password
                />
              </el-form-item>

              <!-- 验证码输入框 -->
              <el-form-item prop="code">
                <el-input
                  v-model="form.code"
                  placeholder="请输入验证码"
                  :prefix-icon="Picture"
                  class="captcha-input"
                >
                  <template #append>
                    <div class="captcha-wrapper" @click="refreshCaptcha">
                      <img v-if="captcha" :src="captcha" alt="验证码">
                      <el-icon v-else class="captcha-loading">
                        <Loading/>
                      </el-icon>
                    </div>
                  </template>
                </el-input>
              </el-form-item>

              <!-- 记住账号和忘记密码 -->
              <el-form-item class="form-actions">
                <el-checkbox v-model="form.remember" class="remember-me">
                  记住账号
                </el-checkbox>
                <el-link :underline="false" class="forgot-password" @click="showForgetPasswordDialog">
                  忘记密码?
                </el-link>
              </el-form-item>

              <!-- 登录按钮 -->
              <el-form-item>
                <el-button
                  type="primary"
                  class="login-button"
                  :loading="loginLoading"
                  @click="handleLogin"
                >
                  {{ loginLoading ? '登录中...' : '登 录' }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 登录页脚 - 其他登录方式 -->
          <div class="login-footer">
            <el-divider class="divider">
              <span class="divider-text">其他登录方式</span>
            </el-divider>
            <div class="login-methods">
              <el-tooltip content="微信登录" placement="top">
                <el-icon class="login-method" @click="loginWithWechat">
                  <ChatDotRound/>
                </el-icon>
              </el-tooltip>
              <el-tooltip content="QQ登录" placement="top">
                <el-icon class="login-method" @click="loginWithQQ">
                  <Iphone/>
                </el-icon>
              </el-tooltip>
              <el-tooltip content="微博登录" placement="top">
                <el-icon class="login-method" @click="loginWithWeibo">
                  <Platform/>
                </el-icon>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- 页脚信息区域 -->
      <div class="footer-info">
        <div class="info-group">
          <!-- 备案号信息 -->
          <div class="info-item" v-if="systemInfo['备案号']?.paramValue">
            <el-icon class="info-icon">
              <Document/>
            </el-icon>
            <span>备案号：{{ systemInfo['备案号'].paramValue || '暂无备案信息' }}</span>
          </div>
          <!-- 学校地址信息 -->
          <div class="address-group">
            <template v-if="systemInfo['学校地址']?.paramValue">
              <div class="address-item">
                <template v-for="(address, index) in systemInfo['学校地址'].paramValue.split('|')" :key="index">
                  <el-icon class="info-icon">
                    <Location/>
                  </el-icon>
                  <span>{{ address }}</span>
                </template>
              </div>
            </template>
          </div>
        </div>

        <!-- 版权信息 -->
        <div class="copyright">
          © {{ systemInfo['学校名称']?.paramValue || '中国农业大学' }} 版权所有 |
          技术支持：{{ techSupport || '北京三易拓科技有限公司' }}
        </div>
      </div>
    </div>
  </div>

  <!-- 系统信息加载中的加载状态 -->
  <div v-else class="loading-wrapper">
    <el-icon class="loading-icon">
      <Loading/>
    </el-icon>
    <span>系统信息加载中...</span>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, computed, watch, nextTick} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import {
  User,
  Lock,
  Picture,
  Iphone,
  ChatDotRound,
  Platform,
  Loading,
  Connection,
  Key,
  Location,
  Document,
} from '@element-plus/icons-vue';
import {getToken} from '@/utils/token-util';
import {usePageTab} from '@/utils/use-page-tab';
import {login, getCaptcha} from '@/api/login';
import {getOAuthLoginUrl} from '@/views/OAuthCallback/api/index.js';
import {useUserStore} from '@/store/modules/user.js';
import {getImageUrl} from '@/utils/common_bak2.js';
import {storeToRefs} from 'pinia';

const router = useRouter();
const {goHomeRoute, cleanPageTabs} = usePageTab();

// 使用用户存储库
const userStore = useUserStore();
// 从store中解构系统信息
const {systemInfo} = storeToRefs(userStore);
// 技术支持单位
const techSupport = ref('北京三易拓科技有限公司');
// 当前激活的登录标签页
const activeTab = ref('account');

// 系统信息是否加载完成
const isSystemInfoLoaded = computed(() => !!systemInfo.value);

// 动态主题配置计算属性
const themeConfig = computed(() => {
  console.log(systemInfo.value?.themeConfig);
  // 如果后端有配置则使用后端配置
  if (systemInfo.value?.themeConfig) {
    return systemInfo.value?.themeConfig;
  }
});

// 计算CSS变量
const cssVars = computed(() => {
  return {
    '--primary-color': themeConfig.value.primary,
    '--gradient-start': themeConfig.value.gradientStart,
    '--gradient-end': themeConfig.value.gradientEnd,
    '--primary-light': themeConfig.value.light,
    '--primary-dark': themeConfig.value.dark,
    '--primary-text': themeConfig.value.text,
    '--bg-start': themeConfig.value.bgStart,
    '--bg-end': themeConfig.value.bgEnd,
    '--card-bg': themeConfig.value.card,
  };
});

// 表单引用
const formRef = ref();
// 登录加载状态
const loginLoading = ref(false);

// 表单数据
const form = reactive({
  username: '',
  password: '',
  code: '',
  cid: '',
  remember: true,
});

// 图形验证码
const captcha = ref('');

// 表单验证规则
const rules = {
  username: [{required: true, message: '请输入学号/工号', trigger: 'blur'}],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}],
  code: [{required: true, message: '请输入验证码', trigger: 'blur'}],
};

/**
 * 初始化验证码
 */
const initCaptcha = async () => {
  try {
    captcha.value = '';
    const data = await getCaptcha();
    captcha.value = data.base64;
    form.cid = data.cid;
  } catch (error) {
    handleError(error, '获取验证码失败');
  }
};

/**
 * 刷新验证码
 */
const refreshCaptcha = () => {
  initCaptcha();
  form.code = '';
};

/**
 * 切换登录标签页
 * @param {string} tab - 要切换到的标签页标识
 */
const switchTab = (tab) => {
  activeTab.value = tab;
  if (tab === 'account') {
    initCaptcha();
  }
};

/**
 * 跳转到统一身份认证
 */
const redirectToUnifiedAuth = async () => {
  try {
    const result = await getOAuthLoginUrl();
    if (result) {
      window.location.href = result;
    }
  } catch (error) {
    handleError(error, '获取认证地址失败');
  }
};

/**
 * 处理登录操作
 */
const handleLogin = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    loginLoading.value = true;

    // 加密表单数据
    const encryptedData = {
      username: encrypt(form.username),
      password: encrypt(form.password),
      code: encrypt(form.code),
      cid: encrypt(form.cid),
      remember: form.remember,
    };

    // 调用登录接口
    await login(encryptedData);
    ElMessage.success('登录成功');
    cleanPageTabs();
    goHome();
  } catch (error) {
    handleError(error, '登录失败');
    refreshCaptcha();
    insertScript();
  } finally {
    loginLoading.value = false;
  }
};

/**
 * 跳转到首页
 */
const goHome = () => {
  const {query} = router.currentRoute.value;
  goHomeRoute(query.from);
};

/**
 * 显示忘记密码对话框
 */
const showForgetPasswordDialog = () => {
  ElMessage.info('请前往信息网络中心重置密码');
};

// 第三方登录方法
const loginWithWechat = () => ElMessage.info('微信登录功能即将开放');
const loginWithQQ = () => ElMessage.info('QQ登录功能即将开放');
const loginWithWeibo = () => ElMessage.info('微博登录功能即将开放');

/**
 * 插入安全脚本
 */
const insertScript = () => {
  const scriptElement = document.createElement('script');
  scriptElement.type = 'text/javascript';
  scriptElement.async = true;
  scriptElement.src = 'api/sec_js';
  const targetElement = document.getElementsByTagName('head')[0] || document.body;
  targetElement.appendChild(scriptElement);
};

/**
 * 统一错误处理
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 */
const handleError = (error, defaultMessage = '请求失败') => {
  const message = error.response?.data?.message || error.message || defaultMessage;
  ElMessage.error(message);
  console.error(error);
};

// // 判断初始化系统信息
// watch(systemInfo.value, (systemInfo) => {
//   nextTick()
//   if (systemInfo) {
//     console.log(systemInfo)
//     // systemInfoLoaded.value = true;
//   }
// }, { immediate: true, deep: true });

// 组件挂载时初始化
onMounted(() => {
  if (router.currentRoute.value.path === '/login') {
    insertScript();
    if (getToken()) {
      goHome();
    } else {
      initCaptcha();
    }
  }
});
</script>

<style lang="scss" scoped>
/* 加载状态样式 */
.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f7fa;

  .loading-icon {
    font-size: 40px;
    color: #409EFF;
    margin-bottom: 20px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 16px;
    color: #606266;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 登录页面主容器 */
.login-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-start) 0%, var(--bg-end) 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;
}

/* 科技感背景样式 */
.tech-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;

  /* 网格背景 */
  .tech-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
  }

  /* 圆形装饰元素 */
  .tech-circles {
    .circle {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.15;

      /* 第一个圆形 */
      &.circle-1 {
        width: 300px;
        height: 300px;
        background: var(--primary-color);
        top: -50px;
        left: -50px;
      }

      /* 第二个圆形 */
      &.circle-2 {
        width: 400px;
        height: 400px;
        background: var(--gradient-start);
        bottom: -100px;
        right: -100px;
      }

      /* 第三个圆形 */
      &.circle-3 {
        width: 200px;
        height: 200px;
        background: var(--primary-light);
        top: 50%;
        left: 30%;
      }
    }
  }
}

/* 登录内容容器 */
.login-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 20px;
}

/* 登录卡片样式 */
.login-card {
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  }
}

/* 登录头部样式 */
.login-header {
  padding: 20px 0;
  text-align: center;
  background: linear-gradient(144deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  color: var(--primary-text);
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  /* 高光动画效果 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0) 50%,
        rgba(255, 255, 255, 0.2) 100%
    );
    animation: shine 3s infinite;
  }

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .logo {
      height: 110px;
      object-fit: contain;
    }

    .title-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .login-title {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    letter-spacing: 1px;
  }

  .login-subtitle {
    font-size: 13px;
    opacity: 0.9;
    margin-top: 6px;
    color: var(--primary-text);
  }
}

/* 登录主体内容样式 */
.login-body {
  padding: 5px 30px 10px;

  @media (max-width: 480px) {
    padding: 10px;
  }
}

/* 登录标签页样式 */
.login-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f2f5;
  border-radius: 8px;
  padding: 4px;
  position: relative;

  .login-tab {
    flex: 1;
    text-align: center;
    padding: 12px 0;
    cursor: pointer;
    font-size: 14px;
    color: #64748b;
    position: relative;
    transition: all 0.3s;

    .el-icon {
      margin-right: 6px;
      vertical-align: middle;
      font-size: 18px;
    }

    /* 标签指示器 */
    .tab-indicator {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 3px;
      border-radius: 3px 3px 0 0;
      transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* 激活状态 */
    &.active {
      color: var(--gradient-start);
      font-weight: 500;
      background: white;

      .tab-indicator {
        background: var(--primary-color);
        width: 60%;
      }
    }

    /* 悬停状态 */
    &:hover:not(.active) {
      color: var(--primary-light);
    }
  }
}

/* 统一身份认证内容样式 */
.unified-auth-content {
  padding: 3px 0;
  text-align: center;

  .auth-icon {
    margin: 0 auto 20px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--gradient-light);
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      font-size: 36px;
      color: var(--primary-color);
    }
  }

  h3 {
    font-size: 17px;
    color: #1e293b;
    margin-bottom: 10px;
  }

  p {
    color: #64748b;
    font-size: 13px;
    margin-bottom: 25px;
    line-height: 1.5;
  }

  /* 认证按钮样式 */
  .auth-button {
    margin-bottom: 2px;
    width: 100%;
    height: 44px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
    border: none;
    box-shadow: 0 4px 12px var(--gradient-light);
    transition: all 0.3s ease;
    color: white;
    position: relative;
    overflow: hidden;

    /* 按钮高光效果 */
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
          to bottom right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0) 100%
      );
      transform: rotate(30deg);
      animation: shine 3s infinite;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px var(--primary-light);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .auth-footer {
    text-align: center;
    margin-top: 15px;

    .el-link {
      font-size: 12px;
      color: var(--gradient-start);
    }
  }
}

/* 忘记密码链接样式 */
.forgot-password {
  color: var(--gradient-start);
}

/* 账号密码登录表单样式 */
.account-login-form {
  .el-form-item {
    margin-bottom: 22px !important;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      padding: 1px 15px;
      box-shadow: 0 0 0 1px #e2e8f0;

      &:hover {
        box-shadow: 0 0 0 1px #cbd5e1;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--primary-color);
      }
    }
  }

  /* 验证码输入框样式 */
  .captcha-input {
    :deep(.el-input-group__append) {
      margin-left: 3px;
      padding: 0;
      background: none;
    }
  }

  /* 验证码容器样式 */
  .captcha-wrapper {
    width: 100px;
    height: 40px;
    background: #f8fafc;
    border-radius: 0 8px 8px 0;
    border: 1px solid #e2e8f0;
    border-left: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;

    img {
      height: 100%;
      width: auto;
    }

    .captcha-loading {
      font-size: 18px;
      color: #94a3b8;
    }
  }

  /* 表单操作区域样式 */
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .el-checkbox {
      :deep(.el-checkbox__label) {
        font-size: 13px;
        color: #64748b;
      }
    }

    .el-link {
      font-size: 12px;
    }
  }

  /* 登录按钮样式 */
  .login-button {
    margin-top: 1px;
    width: 100%;
    height: 44px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
    box-shadow: 0 4px 12px var(--gradient-light);
    transition: all 0.3s ease;
    color: white;
    position: relative;
    overflow: hidden;

    /* 按钮高光效果 */
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(
          to bottom right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0) 100%
      );
      transform: rotate(30deg);
      animation: shine 3s infinite;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px var(--gradient-end);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* 登录页脚样式 */
.login-footer {
  margin-top: 25px;

  .divider {
    margin: 15px 0;

    :deep(.el-divider__text) {
      padding: 0 12px;
      font-size: 12px;
      color: #94a3b8;
      background: transparent;
    }
  }

  /* 其他登录方式图标样式 */
  .login-methods {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 15px 0;

    .login-method {
      margin-top: 10px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8fafc;
      border-radius: 50%;
      color: #64748b;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        color: var(--primary-color);
        background: color-mix(in srgb, var(--primary-color) 10%, white);
        transform: translateY(-3px);
      }
    }
  }
}

/* 页脚信息样式 */
.footer-info {
  margin-top: 25px;
  text-align: center;
  font-size: 12px;
  color: #64748b;
  line-height: 1.6;
  padding: 0 15px;

  .info-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    @media (min-width: 768px) {
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      gap: 15px 25px;
    }
  }

  .address-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px 20px;
  }

  .info-item,
  .address-item {
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0.8;
    transition: opacity 0.3s;
    white-space: nowrap;

    &:hover {
      opacity: 1;
    }
  }

  .info-icon {
    font-size: 13px;
    color: #64748b;
    flex-shrink: 0;
  }

  .copyright {
    margin-top: 12px;
    font-weight: 500;
  }
}

/* 高光动画 */
@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 表单错误提示样式调整 */
:deep(.el-form-item__content) {
  & > .el-form-item__error {
    top: 45px;
    font-size: 12px;
  }
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 480px) {
  .login-wrapper {
    padding: 15px;
  }

  .login-header {
    padding: 20px 0;

    .login-title {
      font-size: 20px;
    }

    .login-subtitle {
      font-size: 12px;
    }
  }

  .login-tabs .login-tab {
    font-size: 13px;
    padding: 10px 0;

    .el-icon {
      font-size: 16px;
      margin-right: 4px;
    }
  }

  .account-login-form {
    .captcha-wrapper {
      width: 85px;
    }

    .login-button {
      height: 42px;
      font-size: 14px;
    }
  }

  .footer-info {
    font-size: 11px;

    .address-group {
      flex-direction: column;
      gap: 5px;
    }

    .info-item,
    .address-item {
      flex-direction: column;
      gap: 2px;
      white-space: normal;
    }

    .info-icon {
      display: none;
    }
  }
}
</style>
