<template>
  <ele-card :body-style="{padding: '5px!important',height:(pageHeight+6)+'px' }">
    <template #header>
      <ele-text style="padding: 2px;">{{ data.zbmc }}</ele-text>
    </template>

    <template #extra>
      <el-button text @click="onBack" class="back-btn">
        <IconPark name="return" size="18" strokeWidth="3"/>
        <span>返回</span>
      </el-button>
    </template>
    <ele-pro-table ref="tableRef"
                   flex-table
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   :show-overflow-tooltip="true"
                   tooltip-effect="light"
                   v-model:selections="selections"
                   highlight-current-row
                   style="padding-bottom: 0"
                   :footer-style="{ paddingBottom: '3px' }"
                   :table-style="{  height: (pageHeight-100)+'px', overflow: 'auto' }">
      <template #toolbar>
        <!--        <div style="display: flex; align-items: center;">-->
        <!--          <ele-text style="padding: 4px;cursor: pointer;" type="secondary" size="sm" @click="onBack()">-->
        <!--            <IconPark name="return" size="18" strokeWidth="3"/>-->
        <!--            <span>返回</span>-->
        <!--          </ele-text>-->
        <el-input clearable
                  size="small"
                  style="max-width: 320px;padding-left: 10px;"
                  :maxlength="20"
                  v-model="keywords"
                  placeholder="输入关键词查询加分明细">
          <template #append>
            <el-button size="small" :icon="SearchOutlined"/>
          </template>
        </el-input>
        <!--        </div>-->
      </template>
      <template #jjfmx="{ row }">
        <ele-tooltip content="申请" placement="left" effect="light">
          <el-link type="primary" underline="never"
                   @click="openEdit(row)">
            {{ row.jjfmx }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
  </ele-card>
  <Edit v-model="showEdit"
        :data="current"
        :organization-id="organizationId"
        :routeType="routeType"
        @done="onDone"/>
</template>

<script setup>
import {ref, watch,} from 'vue';
import Edit from '../components/edit.vue';
import {queryApplyItemDetailList} from "@/views/evaluate/zcsqsh/apply/api/index.js";
import {SearchOutlined} from "@/components/icons/index.js";

import {usePageTab} from "@/utils/use-page-tab.js";
import IconPark from "@/components/IconPark/index.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {removePageTab, reloadPageTab, getRouteTabKey} = usePageTab();

const props = defineProps({
  /** 机构 id */
  organizationId: String,
  routeType: String,
  data: Object,
});
const emit = defineEmits(['done']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);
const keywords = ref(null);

const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  reloadPageTab({fullPath: '/evaluate/zcsqsh/apply/' + props.routeType});
};

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'jjfmx',
    slot: 'jjfmx',
    label: '加减分明细',
    minWidth: 165,
  },
  {
    prop: 'sffdz',
    label: '是否范围值',
  },
  {
    columnKey: 'jfz',
    label: '分值',
    slot: 'jfz',
    formatter: (row) => {
      let rVal = ''
      if (row.sffdz === '是') rVal = row.zxz + '~' + row.zdz;
      if (row.sffdz === '否') rVal = row.gdfz;
      return rVal
    }
  },
  {
    prop: 'sfxs',
    label: '是否显示',
  },
  {
    prop: 'sort',
    label: '排序',
    sortable: 'custom',
    width: 90,
  },
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({pages, where, orders}) => {
  return queryApplyItemDetailList({
    ...where,
    ...orders,
    ...pages,
    itemId: props.organizationId
  });
};

const onDone = () => {
  emit('done');
}

/** 搜索 */
const reload = (where) => {
  tableRef["value"]?.reload?.({page: 1, where});
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  row.zbmc = props.data.zbmc;
  current.value = row ?? null;
  showEdit.value = true;
};

// 监听机构 id 变化
watch(
  () => props.organizationId,
  () => {
    searchRef["value"]?.resetFields?.();
    reload({});
  }
);

</script>
