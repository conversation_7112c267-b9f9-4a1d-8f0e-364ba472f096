<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search
          @search="searchReload"
          ref="searchRef"
          :userType="userType"
          :routeType="routeType"
        />
      </template>
      <!-- 表格  loadOnCreated：默认不请求数据，等待search中的年份被赋值后再进行请求-->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :loadOnCreated="false"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #xypf="{ row }">
          <el-input-number
            :max="5"
            :min="0"
            controls-position="right"
            class="ele-fluid"
            :model-value="row.xypf"
            size="small"
            :precision="2"
            @update:modelValue="(value) => updateValue(row, value)"
            @blur="handleUpdate(row)"
          />
        </template>
        <template #toolbar v-if="userType === 'teacher'">
          <ele-tooltip
            content="计算逻辑：去掉一个最高分和一个最低分，并以算术平均方法计算出最终分值"
            effect="light"
            placement="top-start"
          >
            <el-button
              size="small"
              plain
              class="ele-btn-icon"
              @click="summaryResult()"
            >
              计算互评结果
            </el-button>
          </ele-tooltip>
          <el-button size="small" @click="openUpdateScore()">
            批量学院评分
          </el-button>
          <el-button class="ele-btn-icon" size="small" @click="openImport">
            导入
          </el-button>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="exportData()"
          >
            导出
          </el-button>
          <!-- <el-button size="small" plain class="ele-btn-icon" @click="remove()">
            删除
          </el-button> -->
        </template>
        <template #xshpf="{ row }">
          <div class="link-text" @click="toTeacherSurvey(row)">{{
            row.xshpf
          }}</div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 导入弹窗 -->
    <Import v-model="showImport" @done="reload" :configId="configId" />
    <!-- 批量更新 -->
    <UpdateScore
      v-model="showUpdateScore"
      :updataScoreDatas="updataScoreDatas"
      @done="reload"
    />
    <!-- 评分详情弹窗 -->
    <detail v-model="showDetail" :data="current" />
  </ele-page>
</template>

<script setup>
  import { computed, nextTick, onMounted, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage } from 'element-plus';
  import {
    operationEvaluateReviewScore,
    getEvaluatePeerReviewScorePage,
    removesEvaluateReviewScore,
    updateDeptScore
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { ElLoading } from 'element-plus';
  import Search from './components/search.vue';
  import Import from './components/import.vue';
  import Detail from './details/index.vue';
  import UpdateScore from './components/updateScore.vue';
  import { formMateSearchDataToStr } from '@/utils/common.js';
  import { getToken } from '@/utils/token-util';
  import { usePageTab } from '@/utils/use-page-tab.js';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();

  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];
  let userType = pathArray[4]; // 管理端：teacher  学生端：student
  const showDetail = ref(false);
  const current = ref({});

  /** 表格实例 */
  const tableRef = ref(null);
  /** 检索条件 */
  const searchRef = ref(null);

  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});
  /** 导入弹窗 */
  const showImport = ref(false);
  /** 批量更新弹窗 */
  const showUpdateScore = ref(false);
  /** 加载状态 */
  const loading = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'cpnf',
      label: '测评年份',
      width: 85
    },
    { prop: 'xgh', label: '学号', width: 100 },
    { prop: 'xm', label: '姓名' },
    { prop: 'xymc', label: '学院' },
    { prop: 'zymc', label: '专业' },
    { prop: 'njmc', label: '年级' },
    { prop: 'bjmc', label: '班级' },
    { prop: 'pyccmc', label: '培养层次' },
    { prop: 'xshpf', slot: 'xshpf', label: '学生互评平均分' },
    { prop: 'xypf', slot: 'xypf', label: '学院评分' }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);
  const configId = ref();

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getEvaluatePeerReviewScorePage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
    res.list.map((item) => {
      item.newXypf = item?.xypf;
      if (item.scoreDetail) {
        let scoreDetail = JSON.parse(item.scoreDetail);
        Object.entries(scoreDetail).map(([key, value]) => {
          item[key] = value;
        });
      }
    });
    return {
      list: res.list,
      count: res.count
    };
  };

  const searchReload = (where) => {
    console.log('where :>> ', where);
    searchWhere.value = where;
    configId.value = where.configId;
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const summaryResult = () => {
    const where = searchRef.value.initModel;
    let data = { ...where };
    delete data.title;
    data = removeEmptyProperties(data);
    console.log('data :>> ', data);
    operationEvaluateReviewScore(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        searchReload(data);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 去除对象中值为""、null、undefined的属性" */
  const removeEmptyProperties = (obj) => {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {});
  };

  const updateValue = (row, value) => {
    row.newXypf = value;
  };

  const handleUpdate = (row) => {
    console.log('handleUpdate :>> ', row);
    if (
      row?.newXypf == null ||
      (row.newXypf && row.xypf == row?.newXypf?.toFixed(2))
    )
      return;
    row.xypf = row?.newXypf?.toFixed(2);
    row.configId = row.configId ? row.configId : searchWhere.value.configId;
    console.log('row :>> ', row);
    updateDeptScore([row])
      .then((msg) => {
        EleMessage.success(msg);
        searchReload(searchWhere.value);
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.xm).join(', ') + '”的汇总结果吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesEvaluateReviewScore(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /**
   * 打开学生互评详情
   */
  const toTeacherSurvey = (row) => {
    console.log('row :>> ', row);
    current.value = row;
    showDetail.value = true;
    // removePageTab({ key: getRouteTabKey() });
    // push({
    //   path:
    //     '/evaluate/student-evaluate/details/' +
    //     userType +
    //     '/' +
    //     row.configId +
    //     '/' +
    //     row.xgh
    //   // query: { cpzb: survey.label, bcprxm: row.bcprxm }
    // });
  };
  /** 根据检索条件导出数据 */
  const exportData = () => {
    let where = { ...searchRef.value.initModel };
    delete where.title;
    loading.value = true;
    let searchStr = formMateSearchDataToStr(where);
    console.log('searchStr :>> ', searchStr);
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/evaluate/evaluate-review-score/exportData?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };
  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };
  const updataScoreDatas = computed(() => {
    let res = [];
    selections.value.map((item) => {
      let obj = { ...item };
      if (!obj.configId) {
        obj.configId = searchWhere.value.configId;
      }
      res.push(obj);
    });
    return res;
  });
  /** 批量评分 */
  const openUpdateScore = () => {
    const rows = selections.value;
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    showUpdateScore.value = true;
  };
</script>

<script>
  export default {
    name: 'TEACHERRESULTINDEX'
  };
</script>
<style lang="scss" scoped>
  .link-text {
    color: var(--el-color-primary);
    cursor: pointer;
  }
</style>
