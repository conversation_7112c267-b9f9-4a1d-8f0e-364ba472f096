<!--劳育成绩/体育成绩/美育成绩-->
<template>
  <ele-page
    hide-footer
    flex-table
    :style="{ height: pageHeight + 62 + 'px', overflow: 'auto' }"
  >
    <ele-card
      flex-table
      :header-style="{ fontWeight: 'unset!important' }"
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <search @search="reload" :routeTypeSv="routeTypeSv" />
      </template>
      <!--      <el-divider style="margin: 3px 0px 3px; opacity: 0.6"/>-->
      <!-- 表格 -->
      <ele-pro-table
        v-if="configId"
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <div style="display: flex; align-items: center">
            <!--            <el-select v-if="setEnterType!=='config'"-->
            <!--                       style="width: 260px; margin-right: 15px;"-->
            <!--                       size="small" v-model="configId" placeholder="请选择测评年份"-->
            <!--                       @change="selectCpnfChange">-->
            <!--              <el-option v-for="item in cpnfOptions"-->
            <!--                         :key="item.value"-->
            <!--                         :label="item.label"-->
            <!--                         :value="item.value"/>-->
            <!--            </el-select>-->

            <el-button class="ele-btn-icon" size="small" @click="openImport">
              导入
            </el-button>
            <el-button size="small" @click="exportBas()"> 导出 </el-button>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <import
      v-model="showImport"
      :enterType="routeTypeSv"
      :configId="configId"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { comTableCloumns } from '@/utils/common_bak2.js';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import Import from './components/import.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { getEvaluateConfig } from '@/views/evaluate/evaluate-config/api/index.js';
  import { getScore } from '@/views/evaluate/score/api/index.js';
  import Search from './components/search.vue';

  const props = defineProps({
    setEnterType: String, //配置页面过来的-config
    setConfigId: String, //当前参数ID
    routerTypeSv: String, //成绩类型-lycj，mycj，tycj，tyhdcyjf(体育活动成绩加分)
    RandomString: String,
    configRouterType: String //参数对应的type
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { params, path, meta } = unref(currentRoute);
  let pathArray = path.split('/');
  let routeType = pathArray[3];
  let routeTypeSv = pathArray[4]; //成绩类型 lycj，mycj，tycj，tyhdcyjf(体育活动成绩加分)
  console.log(pathArray, routeType, routeTypeSv);
  const { removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle } =
    usePageTab();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  /** 表格实例 */
  const tableRef = ref(null);
  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'cpnf',
      label: '测评年份'
    },
    {
      prop: 'score',
      label: '成绩'
    },
    ...comTableCloumns(),
    {
      prop: 'pyccmc',
      label: '培养层次'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  const reload = (where) => {
    lastWhere.value = where;
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getScore('/score/score-' + routeTypeSv + '/page', {
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      configId: configId.value
    });
  };

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 导出excel */
  const exportBas = () => {
    window.location.href =
      BASE_URL +
      'api/score/score-' +
      routeTypeSv +
      '/exportData?access_token=' +
      accessToken +
      '&configId=' +
      props.configId;
  };

  const cpnfOptions = ref([]);
  const configId = ref(null);

  /** 查询 */
  const queryEvaluateConfig = async () => {
    const rData = await getEvaluateConfig({ type: routeType });
    let rArray = [];
    if (rData) {
      let obj = {};
      rData.forEach((d) => {
        obj = {
          value: d.id,
          label: d.title
        };
        rArray.push(obj);
      });
      if (rArray.length > 0) {
        configId.value = props?.setConfigId ?? rArray[0].value;
      }
    }
    cpnfOptions.value = rArray;
  };
  const selectCpnfChange = (event) => {
    if (event) {
      configId.value = event;
      reload();
    }
  };

  watch(
    () => props.RandomString,
    (RandomString) => {
      if (RandomString) {
        routeType = props.configRouterType;
        routeTypeSv = props.routerTypeSv;
        configId.value = props.setConfigId;
      }
    },
    { immediate: true }
  );

  queryEvaluateConfig();
</script>

<script>
  export default {
    name: 'SCOREINDEX'
  };
</script>
