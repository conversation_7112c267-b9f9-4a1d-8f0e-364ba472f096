<!--课程成绩结果-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :header-style="{ fontWeight: 'unset!important' }"
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <search @search="reload" />
      </template>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <!--          <el-button class="ele-btn-icon" size="small"-->
          <!--                     @click="openEdit">-->
          <!--            添加-->
          <!--          </el-button>-->
          <!--          <el-button class="ele-btn-icon" size="small"-->
          <!--                     @click="remove()"> 删除-->
          <!--          </el-button>-->
          <el-button size="small" @click="showImportListData = true">
            导入
          </el-button>
          <el-button size="small" @click="exportBas('scoreResult')">
            导出
          </el-button>
          <el-button size="small" @click="showImport = true">
            上传导出模版
          </el-button>
        </template>
        <template #kcm="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.kcm }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit" :data="current" @done="reload" />
    <!--            :enterType="routeTypeSv"-->
    <!--            :configId="configId"-->
    <import v-model="showImport" @done="reload" module="scoreResult" />
    <import-list-data
      v-model="showImportListData"
      @done="reload"
      module="scoreResult"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import Edit from './components/edit.vue';
  import { comTableCloumns } from '@/utils/common_bak2.js';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { ElMessageBox } from 'element-plus';
  import {
    getScoreResultPage,
    removes
  } from '@/views/evaluate/score/score-result/api/index.js';
  import { getProjectTemplateCheck } from '@/views/zizhu/api/index.js';
  import '@/views/evaluate/score/score-result/index.vue';
  import Import from '@/views/evaluate/score/score-result/components/import.vue';
  import ImportListData from '@/views/evaluate/score/score-result/components/import-list-data.vue';
  import Search from '@/views/evaluate/score/score-result/components/search.vue';

  const { currentRoute, push } = useRouter();
  const { params, path, meta } = unref(currentRoute);
  let pathArray = path.split('/');
  console.log(pathArray);
  const { removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle } =
    usePageTab();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const props = defineProps({});

  const showEdit = ref(false);
  const current = ref(null);

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 表格实例 */
  const tableRef = ref(null);
  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xnxq',
      label: '学年学期',
      width: 95
    },
    {
      prop: 'cpnf',
      label: '测评年份',
      width: 85
    },
    ...comTableCloumns(),
    // {
    //   prop: 'gxcj',
    //   label: '各项成绩',
    // },
    {
      prop: 'jqcj',
      label: '加权成绩'
    },
    {
      prop: 'pjcj',
      label: '平均成绩'
    },
    {
      prop: 'zrs',
      label: '总人数'
    },
    {
      prop: 'xyrs',
      label: '学院人数'
    },
    {
      prop: 'zyrs',
      label: '专业人数'
    },
    {
      prop: 'bjrs',
      label: '班级人数'
    },
    {
      prop: 'jqcjpm',
      label: '加权成绩排名'
    },
    {
      prop: 'jqcjpmbfb',
      label: '加权成绩排名百分比'
    },
    {
      prop: 'jqcjxypm',
      label: '加权成绩学院排名'
    },
    {
      prop: 'jqcjxypmbfb',
      label: '加权成绩学院排名百分比'
    },
    {
      prop: 'jqcjzypm',
      label: '加权成绩专业排名'
    },
    {
      prop: 'jqcjzypmbfb',
      label: '加权成绩专业排名百分比'
    },
    {
      prop: 'jqcjbjpm',
      label: '加权成绩班级排名'
    },
    {
      prop: 'jqcjbjpmbfb',
      label: '加权成绩班级排名百分比'
    },
    {
      prop: 'pjcjpm',
      label: '平均成绩排名'
    },
    {
      prop: 'pjcjpmbfb',
      label: '平均成绩排名百分比'
    },
    {
      prop: 'pjcjxypm',
      label: '平均成绩学院排名'
    },
    {
      prop: 'pjcjxypmbfb',
      label: '平均成绩学院排名百分比'
    },
    {
      prop: 'pjcjzypm',
      label: '平均成绩专业排名'
    },
    {
      prop: 'pjcjzypmbfb',
      label: '平均成绩专业排名百分比'
    },
    {
      prop: 'pjcjbjpm',
      label: '平均成绩班级排名'
    },
    {
      prop: 'pjcjbjpmbfb',
      label: '平均成绩班级排名百分比'
    }
    // ...comTableCloumns()
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  const reload = (where) => {
    console.log(where);
    lastWhere.value = where;
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getScoreResultPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /** 删除单个 */
  const remove = (row) => {
    console.log('删除单个===', row);
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.kcm).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 导入请求状态 */
  const loading = ref(false);
  const showImport = ref(false);
  const showImportListData = ref(false);

  /** 导出前先判断下是否有模版在下载 */
  const exportBas = (module) => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    getProjectTemplateCheck({
      module: module,
      templateType: 'list'
    })
      .then((data) => {
        if (data) {
          // let type = templateType === "sqb" ? "申请表" : "汇总表"
          const rows = selections.value;
          let confirmMsg = '';
          if (!rows.length) {
            confirmMsg = '您确定要批量导出';
          } else {
            confirmMsg = '您确定要导出' + rows.map((d) => d.xm).join(', ');
          }
          ElMessageBox.confirm(confirmMsg, '系统提示', {
            type: 'warning',
            draggable: true
          })
            .then(() => {
              setTimeout(() => {
                //     // templateType
                window.location.href =
                  BASE_URL +
                  'api/score/course-score-result/exportData?access_token=' +
                  accessToken +
                  '&module=' +
                  module +
                  '&templateType=list' +
                  '&id=' +
                  rows.map((d) => d.id);
                //     loading.value = false;
              }, 600);
            })
            .catch(() => {});
        }
      })
      .catch((e) => {
        let confirmMsg = '对不起，你要导出的模版文件不存在！';
        // EleMessage.error('对不起，你要导出的模版文件不存在！');
        ElMessageBox.confirm(confirmMsg, 'Warning', {
          confirmButtonText: '上传导出模版',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            // EleMessage.success('completed！');
            showImport.value = true;
          })
          .catch(() => {
            // EleMessage.error('对不起，你要导出的模版文件不存在！');
          });
      });
  };
</script>

<script>
  export default {
    name: 'SCORERESULTINDEX'
  };
</script>
