<template>
  <ele-card v-if="currentGroup && isVisible"
            :key="currentGroup.id"
            :body-style="{height:'auto',padding: '10px 5px 0 10px !important'}"
            :header="`${currentGroup.title}`">
    <div v-if="formItems" :class="isReadonly?'border-only-bottom' :''">
      <el-form ref="proFormRef"
               v-if="formReady"
               :validate-on-rule-change="false"
               :model="formModel"
               :label-width="labelWidth"
               :label-position="labelPosition"
               :rules="formRules"
               size="small"
               @submit.prevent="">
        <el-row :gutter="8">
          <template v-for="item in formItems" :key="item.key">
            <el-col :span="item.colProps" v-if="shouldRenderItem(item)">
              <ProFormItem :item="item"
                           :model="formModel"
                           :error="proFormRef?.fields?.[item.prop]?.validateState === 'error'"
                           @onDoneDeleteFile="handleOnDoneDeleteFile"
                           @updateItemValue="(prop,value) => updateFormValue(item,prop, value,'')">
                <template
                  v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                  #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"></slot>
                </template>
              </ProFormItem>
            </el-col>
          </template>
        </el-row>
        <div v-if="showEmptyState" style="text-align: center;">
          <img style="width: 80px;margin-left: 13px;" src="/src/assets/u65.png">
          <ele-text style="padding:0" type="secondary">
            暂无数据
          </ele-text>
        </div>
      </el-form>
    </div>
  </ele-card>
</template>

<script setup>
import {ref, computed, watch, nextTick, onMounted, onUnmounted} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {getFieldProps, createFieldObject, createDetailAddressField} from '@/utils/form/field-utils.js';
import {handleRegionsData, handleImageUploadData, handleFileUploadData} from '@/utils/form/data-handlers.js';
import {nodeFormFieldList} from '@/views/evaluate/approval/api/index.js';
import {getFormApplyApplicationInfo, getFormApplyFieldList} from '@/views/zizhu/apply/api/index.js';
import {getConditionField} from '@/views/zizhu/condition/api/field-index.js';
import {getFieldList, getPersonInfoByXgh, getPersonInfoData} from '@/views/personInfo/st/api/index.js';
import {getFormTemplateField} from '@/views/zizhu/api/form-template-field-index.js';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import {getApprovalNodeFormFieldList} from '@/views/zizhu/approval/api/index.js';
import {nodeFormFieldListQgzxJob} from '@/views/qgzx/qgzx-job-approval/api/index.js';
import {nodeFormFieldListQgzxStudent} from '@/views/qgzx/qgzx-student-approval/api/index.js';
import {comColumns} from '@/views/qgzx/qgzx-job-approval/utils/approval.js';
import {comColumns as comColumnsJobApply} from '@/views/qgzx/qgzx-sutdent-apply/utils/apply.js';

// Constants
const READONLY_MODES = ['preview', 'sqpreview'];
const BASE_URL = import.meta.env.BASE_URL;
// Props
const props = defineProps({
  labelWidth: {
    type: String,
    default: '100px',
  },
  labelPosition: {
    type: String,
    default: 'right',
  },
  currentGroup: Object,
  routeType: String,
  userType: String,
  currentData: Object,
  queryParams: Object,//根据xgh过滤培养层次
});

// Emits
const emit = defineEmits(['onDoneGroup', 'onDoneDeleteFile', 'valid']);

const isReadonly = computed(() => ['preview', 'sqpreview'].includes(props.currentGroup?.enType));

// Refs
const loading = ref(true);
const currentData = ref(props?.currentData ?? {});
const fieldData = ref([]);
const formItems = ref([]);
const formModel = ref({});
const proFormRef = ref(null);
const formReady = ref(false);

const showEmptyState = computed(() => {
  return (!formItems.value || formItems.value.length === 0) && props.currentGroup.infoType !== 'approval';
});

const isReadonlyMode = computed(() => {
  return READONLY_MODES.includes(props.currentGroup?.enType);
});

// Data Fetching Strategies
const dataFetchStrategies = {
  base: queryFormTemplateField,
  zhcpApproval: queryNodeFormFieldList,
  QgzxJobApproval: queryNodeFormFieldListQgzxJob,
  QgzxStudentApproval: queryNodeFormFieldListQgzxStudent,
  QgzxJobApply: queryQgzxJobApply,
  approval: queryApprovalNodeFormFieldList,
  apply: queryFormApplyFieldList,
  list: queryFormApplyFieldList,
  conditionSet: queryConditionField,
  studentSearch: queryStudentSearchField,
  default: queryDictionaryField,
};

// Field Handlers
const fieldHandlers = {
  regions: handleRegionsField,
  imageUpload: handleImageField,
  fileUpload: handleFileField,
  multipleSelect: handleMultipleSelectField,
  default: handleDefaultField,
};

// Methods
function shouldRenderItem(item) {
  // 只读模式显示所有非隐藏字段
  if (isReadonlyMode.value) return item.showFlag !== '否';
  // 编辑模式显示可修改字段
  return item.showFlag === '是' || item.selfModifyFlag === '是';
}

function getUpdateType(item) {
  return item.showFlag === '是' && item.selfModifyFlag === '是' ? 'change' : '';
}

// 确保数据加载时序
const isInitialLoad = ref(true);//添加验证控制标志
async function fetchDataBasedOnGroupType(group) {
  if (!group) return;
  isInitialLoad.value = true;
  loading.value = true;
  try {
    // 1. 先加载字段配置
    const strategy = dataFetchStrategies[group.infoType] || dataFetchStrategies.default;
    await strategy(group);

    // 2. 再加载数据（如果有）
    if (group.infoType === 'apply' && !group.xgh) {
      await queryFormApplyApplicationInfo();
    } else if (group.xgh) {
      await queryPersonInfoByXgh();
    }

    // 3. 最后格式化数据
    await formatFormData(fieldData.value);
  } finally {
    loading.value = false;
    // 延迟设置以避免初始验证
    setTimeout(() => isInitialLoad.value = false, 100);
  }
}

const formRules = computed(() => {
  if (isInitialLoad.value) return {}; // 初始加载时不返回规则

  const rules = {};
  formItems.value.forEach(item => {
    if (item.rules && item.rules.length > 0) {
      rules[item.prop] = item.rules;
    }
  });
  return rules;
});
// async function fetchDataBasedOnGroupType(group) {
//   if (!group) return;
//
//   loading.value = true;
//   try {
//     // 1. 先加载字段配置
//     const strategy = dataFetchStrategies[group.infoType] || dataFetchStrategies.default;
//     await strategy(group);
//
//     // 2. 再加载数据（如果有）
//     if (group.infoType === 'apply' && !group.xgh) {
//       await queryFormApplyApplicationInfo();
//     } else if (group.xgh) {
//       await queryPersonInfoByXgh();
//     }
//
//     // 3. 最后格式化数据
//     await formatFormData(fieldData.value);
//
//   } catch (e) {
//     console.error('数据加载失败:', e);
//   } finally {
//     loading.value = false;
//   }
// }

async function formatFormData(fields) {
  if (!fields?.length) {
    formItems.value = [];
    formModel.value = {};
    return;
  }
  // 初始化 model 对象，确保包含所有字段
  const initialModel = fields.reduce((model, field) => {
    // 初始化为 undefined 而不是 null，避免触发验证
    if (field.controlType === 'regions') {
      model[field.fieldEn] = undefined;
      model[`${field.fieldEn}_regionsDetail`] = undefined;
    } else {
      model[field.fieldEn] = undefined;
    }
    return model;
  }, {});

  // 创建 items 数组
  const items = fields.flatMap((field, index) => {
    const {props, type, typeKey} = getFieldProps(field);
    const item = createFieldObject(field, index, {props, type, typeKey});

    // 添加验证规则
    item.rules = generateRules(field);

    // 处理 regions 类型的特殊字段
    if (field.controlType === 'regions') {
      return [item, createDetailAddressField(field, index)];
    }
    return item;
  });
  //统一入口处理只读
  items.forEach((init => {
      if (['preview', 'sqpreview'].includes(props.currentGroup.enType)) {
        init.showFlag = init.showFlag !== '否' ? 'readonly' : init.showFlag;
        init.selfModifyFlag = init.showFlag;
      }
    }
  ));
  formItems.value = items;
  formModel.value = initialModel;
  // 3. 等待DOM更新完成
  await nextTick();
  // 填充已有数据
  console.log(props.currentGroup, currentData.value);
  // 4. 如果有数据，填充数据
  if (currentData.value) {
    await handleExistingData(items);
  }
  // 5. 标记表单准备就绪
  formReady.value = true;
}

function generateRules(field) {
  const rules = [];
  // 必填验证
  if (field.required === '是') {
    rules.push({
      required: true,
      message: `${field.fieldZh}不能为空`,
      trigger: field.controlType === 'input' ? 'blur' : 'change',
    });
  }

  // 根据字段类型添加特定验证
  switch (field.controlType) {
    case 'input':
      if (field.dataType === 'number') {
        rules.push({type: 'number', message: '必须输入数字'});
      } else if (field.dataType === 'email') {
        rules.push({type: 'email', message: '邮箱格式不正确'});
      }
      break;
    case 'phone':
      rules.push({pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确'});
      break;
    // 其他字段类型的验证规则...
  }

  return rules;
}

//表单数据填充逻辑
/**
 * 填充已有数据到表单
 * @param {Array} items - 表单字段配置项数组
 */
async function handleExistingData(items) {
  // 1. 解析数据（不修改 currentData.value！）
  const dataToFill = props.currentGroup.infoType === 'conditionSet'
    ? parseConditionValue(currentData.value.conditionValue)
    : currentData.value;

  // 2. 填充到表单模型
  const newModel = { ...formModel.value };
  items.forEach(item => {
    const value = dataToFill[item.prop] ?? dataToFill[getMappedDataKey(item.prop)];
    if (value !== undefined && value !== null) {
      newModel[item.prop] = processFieldValue(item, value);
    }
  });

  // 3. 更新表单模型
  formModel.value = newModel;
  // 确保UI更新
  await nextTick();
  // 触发数据更新事件
  if (props.currentGroup?.listFlag !== '是') {
    emitGroupData();
  }
}

function parseConditionValue(rawValue) {
  try {
    return rawValue && typeof rawValue === 'string' ? JSON.parse(rawValue) : {};
  } catch (e) {
    console.warn('解析conditionValue失败：', e);
    return {};
  }
}

function processFieldValue(item, value) {
  // 特殊字段类型处理
  switch (item.typeKey) {
    case 'regions':
      return handleRegionsData(item, value);
    case 'imageUpload':
      return handleImageUploadData(item, value);
    case 'fileUpload':
      return handleFileUploadData(item, value);
    case 'multipleSelect':
      return handleMultipleSelectValue(value);
    default:
      return value;
  }
}

function handleMultipleSelectValue(value) {
  if (value === null || value === undefined) return [];
  if (Array.isArray(value)) return value;
  if (typeof value === 'string') return value.split(',').filter(Boolean);
  return [];
}

// async function fillExistingData(items) {
//   const updates = items.map(item => {
//     // 处理字段名映射
//     const dataKey = getMappedDataKey(item.prop);
//     let dataValue = currentData.value[dataKey];
//     if (dataValue === undefined || dataValue === null) {
//       return null;
//     }
//     // 特殊字段处理
//     if (item.typeKey === 'regions') {
//       return handleRegionsFieldUpdate(item, dataValue, items);
//     }
//     const handler = fieldHandlers[item.typeKey] || fieldHandlers.default;
//     const value = handler(item, dataValue);
//     return value !== null ? {item, value} : null;
//   }).filter(Boolean);
//   // 批量更新表单
//   updates.forEach(({item, value}) => {
//     formModel.value[item.prop] = value;
//   });
//   // 触发事件
//   if (props.currentGroup?.listFlag !== '是') {
//     emitGroupData();
//   }
// }

function getMappedDataKey(prop) {
  // 处理字段名映射
  const mapping = {
    'xymc': 'xyid',
    'zymc': 'zyid',
    'bjmc': 'bjid',
    'ryzt': 'ryztid',
  };
  return mapping[prop] || prop;
}

function handleRegionsFieldUpdate(item, dataValue, allItems) {
  if (!dataValue) return null;

  let splitData = dataValue.split(',');
  const mainValue = splitData.slice(0, 3);
  const detailValue = splitData[3] || '';

  // 更新详细地址字段
  const detailField = allItems.find(i => i.prop === `${item.prop}_regionsDetail`);
  if (detailField) {
    nextTick(() => {
      formModel.value[detailField.prop] = detailValue;
    });
  }

  return {item, value: mainValue};
}

function handleOnDoneDeleteFile(prop, value) {
  // 触发事件
  emit('onDoneDeleteFile', prop, value);
}

function updateFormValue(item, prop, newVal, mark = '') {
  // 确保使用响应式方式更新
  formModel.value = {
    ...formModel.value,
    [prop]: newVal,
  };

  // 处理关联字段
  if (item.selfFieldLink) {
    handleSelfFieldLink(item, newVal);
  }

  // 处理字段联动
  if (item.fieldLinks) {
    handleFieldLinks(item, newVal, mark);
  }

  // 只读模式处理
  if (isReadonlyMode.value) {
    formItems.value = formItems.value.map(i => ({
      ...i,
      showFlag: 'readonly',
      selfModifyFlag: 'readonly',
    }));
  }
  // 触发事件
  if (props.currentGroup?.listFlag !== '是') {
    emitGroupData();
  }
}

function handleSelfFieldLink(item, value) {
  nextTick(() => {
    if (formItems.value) {
      const linkedItem = formItems.value.find(f => f.selfFieldLink === item.nextField);
      if (linkedItem) {
        linkedItem.props.refresh = value;

        if (item.nextField === 'zymc') {
          linkedItem.props.dicQueryParams.params = {xyid: value};
        } else if (item.nextField === 'bjmc') {
          linkedItem.props.dicQueryParams.params = {zyid: value};
        }
      }
    }
  });
}

function handleFieldLinks(item, value, mark) {
  const showFields = item.fieldLinks.filter(f => f.fieldVal === value);
  if (showFields.length === 0) return;

  showFields.forEach(sf => {
    nextTick(() => {
      if (!isReadonlyMode.value && mark === 'change') {
        formModel.value[sf.linkField] = '';
      }
      formItems.value.forEach(init => {
        if (sf.linkField === init.prop) {
          init.showFlag = isReadonlyMode.value ? 'readonly' : sf.showFlag;

          if (sf.linkFieldDataType) {
            init.props = {
              code: sf.linkFieldDataType,
              filterable: true,
            };
          }

          init.selfModifyFlag = init.showFlag;
        }
      });
    });
  });
}

function emitGroupData() {
  emit('onDoneGroup', {
    groupId: props.currentGroup.id,
    groupName: props.currentGroup.title,
    values: formModel.value,
  });
}

// Field Handlers Implementation
function handleRegionsField(item, dataValue) {
  return handleRegionsData(item, dataValue);
}

function handleImageField(item, dataValue) {
  return handleImageUploadData(item, dataValue);
}

function handleFileField(item, dataValue) {
  return handleFileUploadData(item, dataValue);
}

function handleMultipleSelectField(item, dataValue) {
  // 如果是条件设置模式，直接返回值
  if (props.currentGroup.enType === 'conditionSet') {
    return dataValue;
  }
  // 处理各种可能的输入情况
  if (dataValue === null || dataValue === undefined) {
    return [];
  }
  // 如果已经是数组，直接返回
  if (Array.isArray(dataValue)) {
    return dataValue;
  }
  // 如果是对象，尝试提取值
  if (typeof dataValue === 'object') {
    // 可以尝试从对象的特定属性获取值，或者返回空数组
    return Object.values(dataValue).filter(val => val !== null && val !== undefined);
  }
  // 如果是字符串，按逗号分割
  if (typeof dataValue === 'string') {
    return dataValue.split(',').filter(item => item.trim() !== '');
  }
  // 其他情况返回空数组
  return [];
}

function handleDefaultField(item, dataValue) {
  return dataValue;
}

async function queryFormTemplateField() {
  loading.value = true;
  try {
    const list = await getFormTemplateField({
      projectId: props.currentGroup.projectId,
      groupId: props.currentGroup.id,
      type: props.routeType,
      infoType: props.currentGroup.infoType,
    });
    if (list) {
      list.forEach((item) => {
        item.span = props.currentGroup?.span ?? 8;
        item.showFlag = 'readonly';
        if (item.fieldEn === 'xyid') item.fieldEn = 'xymc';
        if (item.fieldEn === 'zyid') item.fieldEn = 'zymc';
        if (item.fieldEn === 'bjid') item.fieldEn = 'bjmc';
        if (item.fieldEn === 'ryztid') item.fieldEn = 'ryzt';
      });
    }
    fieldData.value = list ?? [];

    // ✅ 关键修改：确保数据加载完成后再初始化表单
    if (props.currentGroup.xgh) {
      await queryPersonInfoByXgh();
    } else {
      await queryPersonInfo();
    }
    await formatFormData(fieldData.value);  // 确保在数据加载后执行

  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    loading.value = false;
  }
}

const queryPersonInfoByXgh = async () => {
  loading.value = true;
  try {
    const data = await getPersonInfoByXgh(props.currentGroup?.userType, props.currentGroup?.xgh);
    currentData.value = data;
    return data; // 返回Promise以便链式调用
  } catch (e) {
    EleMessage.error(e.message);
    throw e;
  } finally {
    loading.value = false;
  }
};

/** 查询用户基本信息 */
const queryPersonInfo = async () => {
  loading.value = true;
  await getPersonInfoData().then((data) => {
    loading.value = false;
    currentData.value = data;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

async function queryNodeFormFieldList() {
  const rData = await nodeFormFieldList({
    applicationId: props.currentGroup.id,
    workflowId: props.currentGroup.workflowId,
    nodeId: props.currentGroup.nodeId,
  });
  if (rData) {
    rData.forEach((d) => {
      d.showFlag = '是';//设置默认可编辑
      d.selfModifyFlag = '是';//设置默认可编辑
    });
    await formatFormData(rData);
    fieldData.value = rData ?? [];
  }
}

async function queryNodeFormFieldListQgzxJob() {
  try {
    const rData = await nodeFormFieldListQgzxJob({
      applicationId: props.currentGroup.id,
      workflowId: props.currentGroup.workflowId,
      nodeId: props.currentGroup.nodeId,
    });
    const baseColumns = comColumns() || [];
    baseColumns.forEach((col) => {
      if (col.fieldEn === 'result') {
        col.moduleCode = 'qgzx_gwsb'; // 勤工助学岗位申报
      }
    });
    if (Array.isArray(rData)) {
      const formattedData = rData.map((d) => ({
        ...d,
        showFlag: '是',
        selfModifyFlag: '是',
      }));
      const mergedData = [...baseColumns, ...formattedData];
      fieldData.value = mergedData;
      await formatFormData(mergedData);
    } else {
      console.warn('字段配置返回数据格式错误:', rData);
    }
  } catch (err) {
    console.error('查询字段配置失败:', err);
  }
}

async function queryQgzxJobApply() {
  try {
    fieldData.value = comColumnsJobApply() || [];
    await formatFormData(fieldData.value);
  } catch (err) {
    console.error('查询字段配置失败:', err);
  }
}

async function queryNodeFormFieldListQgzxStudent() {
  try {
    const rData = await nodeFormFieldListQgzxStudent({
      applicationId: props.currentGroup.id,
      workflowId: props.currentGroup.workflowId,
      nodeId: props.currentGroup.nodeId,
    });
    const baseColumns = comColumns() || [];
    baseColumns.forEach((col) => {
      if (col.fieldEn === 'result') {
        col.moduleCode = 'qgzx_sqgw'; // 勤工助学申请岗位
      }
    });
    if (Array.isArray(rData)) {
      const formattedData = rData.map((d) => ({
        ...d,
        showFlag: '是',
        selfModifyFlag: '是',
      }));
      const mergedData = [...baseColumns, ...formattedData];
      fieldData.value = mergedData;
      await formatFormData(mergedData);
    } else {
      console.warn('字段配置返回数据格式错误:', rData);
    }
  } catch (err) {
    console.error('查询字段配置失败:', err);
  }
}

async function queryApprovalNodeFormFieldList() {
  loading.value = true;
  await getApprovalNodeFormFieldList({
    applicationId: props.currentGroup?.sqId,
    projectId: props.currentGroup?.projectId,
    nodeId: props.currentGroup?.nodeId,
    year: props.currentGroup?.year,
  }).then((list) => {
    loading.value = false;
    if (list) {
      list.forEach((item) => {
        item.span = 12;
        item.showFlag = '是';
      });
      formatFormData(list);
    }
    fieldData.value = list ?? [];
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/**资助项目申请- 查询组的申请字段信息 */
async function queryFormApplyFieldList() {
  loading.value = true;
  getFormApplyFieldList({
    projectId: props.currentGroup.projectId,
    applicationId: props.currentGroup.applicationId,
    groupId: props.currentGroup.id,
    type: props.routeType,
  }).then((list) => {
    loading.value = false;
    fieldData.value = list ?? [];
    formatFormData(fieldData.value);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {

  });
}

async function queryFormApplyApplicationInfo() {
  loading.value = true;
  await getFormApplyApplicationInfo({
    projectId: props.currentGroup?.projectId,
    applicationId: props.currentData?.applicationId,
  }).then((data) => {
    loading.value = false;
    currentData.value = data;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

async function queryConditionField() {
  loading.value = true;
  await getConditionField({conditionId: props.currentGroup?.id}).then(async (list) => {
    loading.value = false;
    if (list) {
      list.forEach(item => {
        item.showFlag = '是';
        item.selfModifyFlag = '是';
        item.span = 24;
      });
      fieldData.value = list;
      await formatFormData(list);
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

async function queryDictionaryField() {
  try {
    const list = await getFieldList(props.routeType, {
      groupId: props.currentGroup?.id,
      groupName: props.currentGroup?.groupName,
      tempField: '否',
      ...props.queryParams,
    });
    fieldData.value = list || [];
  } catch (e) {
    throw e;
  }
}

async function queryStudentSearchField() {
  try {
    const list = await getFieldList(props.routeType, {
      tempField: '否',
      queryShowFlag: '是',
      listFlag: '否',
    });
    fieldData.value = list || [];
  } catch (e) {
    throw e;
  }
}

const validate = async () => {
  try {
    // 多重保护检查
    if (loading.value) {
      console.warn('表单正在加载，跳过验证');
      return false;
    }

    if (!proFormRef.value?.validate) {
      console.warn('表单引用未初始化');
      await nextTick(); // 等待可能的DOM更新

      if (!proFormRef.value?.validate) {
        console.error('表单验证不可用');
        return false;
      }
    }

    await proFormRef.value.validate();
    return true;
  } catch (error) {
    console.error('表单验证失败:', error);
    // 可以在这里添加特定错误的处理逻辑
    if (error?.fields) {
      // 处理具体字段错误
      const firstError = Object.keys(error.fields)[0];
      EleMessage.warning(`请检查${firstError}字段`);
    }
    return false;
  }
};

// 添加一个显示状态的ref
const isVisible = ref(true);
watch(() => props.currentGroup, async (newGroup, oldGroup) => {
  // 添加验证状态控制
  const wasValidating = proFormRef.value?.isValidating;

  if (!newGroup || (oldGroup && newGroup.id !== oldGroup.id)) {
    await resetForm();
  }

  if (newGroup) {
    await fetchDataBasedOnGroupType(newGroup);
  }

  // 如果之前没有验证，清除验证状态
  if (!wasValidating) {
    await nextTick();
    proFormRef.value?.clearValidate();
  }
}, {immediate: true, deep: true});

// 添加重置表单的方法
async function resetForm() {
  // 重置表单验证
  if (proFormRef.value) {
    // 先清除验证状态
    proFormRef.value.clearValidate();
    // 重置内部验证状态
    proFormRef.value.fields?.forEach(field => {
      field.validateState = '';
      field.validateMessage = '';
    });
  }

  // 重置表单数据
  formModel.value = {};
  currentData.value = {};
  fieldData.value = [];
  formItems.value = [];

  // 强制重新渲染组件
  isVisible.value = false;
  await nextTick();
  isVisible.value = true;
}

// 添加组件卸载时的清理
onUnmounted(() => {
  resetForm();
});

// 暴露验证方法
defineExpose({
  proFormRef,
  validate,
  clearValidate: resetForm,  // 直接使用resetForm方法
  resetForm,  // 暴露resetForm方法供外部调用
});
</script>

<style lang="scss" scoped>
@import "../../../../css/border-only-bottom.css";
</style>
