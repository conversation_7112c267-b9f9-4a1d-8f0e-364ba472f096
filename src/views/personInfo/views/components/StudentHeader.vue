<template>
  <div class="student-header">
    <!-- 3D头像区域 -->
    <div class="avatar-section">
      <div class="avatar-3d">
        <!-- 照片显示 -->
        <img
          v-if="showAvatarPhoto"
          :src="avatarPhotoUrl"
          alt="学生头像"
          class="avatar-img"
          :style="avatarStyle"
          @load="handleImageLoad"
          @error="handleImageError"
        />
        <template v-else-if="currentData">
          <img v-if="currentData.xb==='男'"
               src="/male.png"
               :alt="currentData.xb"
               class="avatar-img"/>
          <img v-else-if="currentData.xb==='女'"
               src="/female.png"
               :alt="currentData.xb"
               class="avatar-img"/>
        </template>
        <!--  加载状态-->
        <el-skeleton
          v-else
          class="avatar-loading"
          :style="{ width: `${avatarSize}px`, height: `${avatarSize}px` }"
          variant="circle"
        />
        <!-- 光晕效果 -->
        <div class="avatar-halo"></div>
      </div>
    </div>

    <!-- 学生基本信息区域 -->
    <div class="info-section">
      <!-- 姓名和学号行 -->
      <div class="student-name-row"><h3  class="meta-info">欢迎回来，</h3>
        <h3 class="student-name">
          <span class="highlight">{{ currentData.xm || '--' }}</span>
        </h3>
        <div class="student-meta">
          <span class="student-id">{{ currentData.xgh || '--' }}</span>
        </div>
      </div>

      <!-- 学院专业班级信息 -->
      <div class="meta-info">
        <span>
          {{ currentData.xymc || '--' }} ·
          {{ currentData.zymc || '--' }} ·
          {{ currentData.njmc || '--' }} ·
          {{ currentData.bjmc || '--' }}
        </span>
      </div>
      <!-- 辅导员信息 -->
      <div class="counselors" v-if="currentCounselors.length > 0">
        <div
          class="counselor-item"
          v-for="(counselor, index) in currentCounselors"
          :key="index"
        >
          <el-tooltip
            effect="light"
            :content="`点击拨打 ${counselor.sjh}`"
            placement="right-start"
          >
            <el-tag
              class="counselor-tag"
              @click="callCounselor(counselor.sjh)"
            >
              {{ counselor.roleName }} {{ counselor.xm }} {{ counselor.sjh }}
              <el-icon>
                <Phone/>
              </el-icon>
            </el-tag>
          </el-tooltip>
        </div>
      </div>
      <p>今天是{{ currentDate }}</p>
    </div>

    <!-- 标签云 -->
    <div class="tag-cloud">
      <span
        v-for="(tag, index) in tags"
        :key="index"
        class="tag"
        :style="{
          transform: `rotate(${getRandomRotation()}deg)`,
          backgroundColor: tagColors[index % tagColors.length],
          fontSize: `${getRandomSize()}px`
        }"
        @click="handleTagClick(tag)"
      >
        {{ tag }}
      </span>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch, h, onMounted} from 'vue';
import {Phone} from '@element-plus/icons-vue';
import {getPersonInfo, getPersonInfoData, getStuPersonAdvisor} from '@/views/personInfo/st/api/index.js';
import {ElMessage, ElSkeleton, ElTag, ElTooltip, ElIcon} from 'element-plus';
import {usePageTab} from '@/utils/use-page-tab.js';

const props = defineProps({
  currentXgh: {  // 当前学工号
    type: String,
    required: true,
  },
  routeType: {   // 路由类型
    type: String,
    required: true,
  },
});

const {setPageTabTitle} = usePageTab();

// 响应式数据
const currentData = ref({});          // 当前学生数据
const currentCounselors = ref([]);     // 辅导员列表
const imageLoaded = ref(false);       // 图片是否加载完成
const imageError = ref(false);        // 图片加载是否出错
const loading = ref(true);            // 数据加载状态
const mousePosition = ref({x: 0, y: 0}); // 鼠标位置

// 当前日期
const currentDate = ref('');
const updateCurrentDate = () => {
  const now = new Date();
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  };
  currentDate.value = now.toLocaleDateString('zh-CN', options);
};

// 标签数据
const tags = ['优秀学生', '科技创新', '社团骨干', '志愿者'];
const tagColors = [
  '#FF6B6B', '#4ECDC4', '#FFD93D', '#1A9CFC',
  '#FF8C42', '#A29BFE', '#55EFC4', '#FF66B3',
];

// 头像大小配置
const avatarSize = ref(120); // 默认80px，可根据需要调整

// 图片处理逻辑
const showAvatarPhoto = computed(() => {
  return imageLoaded.value && currentData.value?.photo && !imageError.value;
});

const avatarPhotoUrl = computed(() => {
  try {
    const photo = JSON.parse(currentData.value.photo);
    return photo[0]?.id ? `/api/file/inline/${photo[0].id}` : '';
  } catch {
    return '';
  }
});

const avatarStyle = computed(() => {
  const rotateX = (mousePosition.value.y - 0.5) * 20;
  const rotateY = -(mousePosition.value.x - 0.5) * 20;
  return {
    transform: `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`,
  };
});

// 方法定义
const getRandomRotation = () => Math.random() * 20 - 18;
const getRandomSize = () => 12 + Math.random() * 6;

const callCounselor = (phone) => {
  if (!phone) return;
  window.location.href = `tel:${phone}`;
};

const handleTagClick = (tag) => {
  console.log(`点击标签: ${tag}`);
};

const handleImageLoad = () => {
  imageLoaded.value = true;
  imageError.value = false;
};

const handleImageError = () => {
  imageError.value = true;
};

// 数据获取方法
const queryPersonInfo = async () => {
  try {
    loading.value = true;
    const data = props.currentXgh === 'self'
      ? await getPersonInfoData()
      : await getPersonInfo(props.routeType, props.currentXgh);

    currentData.value = data;
    setPageTabTitle(`${data.xm}-学生画像查看`);

    // 如果有照片则尝试加载
    if (data.photo) {
      imageLoaded.value = true;
      imageError.value = false;
    }
  } catch (e) {
    ElMessage.error(e.message || '获取学生信息失败');
  } finally {
    await initStuPersonAdvisor();
    loading.value = false;
  }
};

const initStuPersonAdvisor = async () => {
  try {
    console.log(currentData.value);
    currentCounselors.value = await getStuPersonAdvisor(currentData.value.xgh);
  } catch (e) {
    ElMessage.error(e.message || '获取辅导员信息失败');
  }
};

// 监听学工号变化
watch(() => props.currentXgh, (val) => {
  if (val) {
    queryPersonInfo();
  }
}, {immediate: true});
// 生命周期钩子
onMounted(() => {
  updateCurrentDate();
});
</script>

<style scoped>
.student-header {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 6px 3px 0 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}


/* 头像容器 */
.avatar-section {
  position: relative;
  flex-shrink: 0;
}

.avatar-3d {
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  perspective: 1000px;
  position: relative;
}

/* 头像图片样式 */
.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease-out;
  background: #f5f7fa;
}

/* 默认头像样式 */
.gender-fallback-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.icon-svg {
  color: #606266;
}

/* 加载状态 */
.avatar-loading {
  border-radius: 50%;
}

/* 光晕效果 */
.avatar-halo {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: radial-gradient(
    circle at center,
    rgba(64, 158, 255, 0.2) 0%,
    transparent 70%
  );
  pointer-events: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .avatar-section {
    --avatar-size: 60px;
  }
}

/* 信息区域样式 */
.info-section {
  flex: 1;
  min-width: 0;
  margin: 5px;
}

.student-name-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.student-name {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
}

.highlight {
  color: #409EFF;
}

.student-meta {
  font-size: 14px;
  color: #64748b;
}

.meta-info {
  margin: 10px 0;
  color: #555;
  font-size: 14px;
}

/* 辅导员标签样式 */
.counselors {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin: 12px 0;

}

.counselor-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  font-size: 13px;
}

.counselor-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 标签云样式 */
.tag-cloud {
  width: 360px;
  height: 160px;
  position: relative;
}

.tag {
  position: absolute;
  display: inline-block;
  border-radius: 15px;
  color: white;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag:hover {
  transform: scale(1.1) !important;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标签定位 */
.tag:nth-child(1) {
  top: 12%;
  left: 22%;
}

.tag:nth-child(2) {
  top: 28%;
  left: 68%;
}

.tag:nth-child(3) {
  top: 18%;
  left: 52%;
}

.tag:nth-child(4) {
  top: 44%;
  left: 30%;
}

.tag:nth-child(5) {
  top: 58%;
  left: 72%;
}

.tag:nth-child(6) {
  top: 66%;
  left: 42%;
}
</style>
